includes:
    - ./vendor/nunomaduro/larastan/extension.neon
    - ./vendor/phpstan/phpstan-deprecation-rules/rules.neon
    - ./vendor/ekino/phpstan-banned-code/extension.neon
#    - ./vendor/phpstan/phpstan-strict-rules/rules.neon
parameters:
    paths:
        - app
        - Modules

    # The level 9 is the highest level
    level: 2

    ignoreErrors:
        - '#Unsafe usage of new static#'
        - '#on Laravel collection, but could have been retrieved#' # возможно нужно подключить ide-helper
        # 1 level errors
        - '#Access to an undefined property#' # возможно нужно подключить ide-helper
        - '#Relation .* is not found in#' # разобраться позже
        # 2 level errors
        - '#Call to an undefined method Illuminate\\Database\\Schema\\ColumnDefinition::references#'
        - '#Call to an undefined method Illuminate\\Contracts\\Pagination\\LengthAwarePaginator::all#'
        - '#Call to an undefined method Illuminate\\Contracts\\Pagination\\LengthAwarePaginator::setCollection#'

        - '#PHPDoc#'
        - '#Method Illuminate\\Filesystem\\FilesystemAdapter::makeDirectory#'
        - '#Binary operation#'
        - '#Call to an undefined method Illuminate\\Database\\Schema\\Blueprint#'
        - '#Call to an undefined method object::save#'
        - '#Cannot access property \$uneditable on array|Illuminate\\Contracts\\Pagination\\LengthAwarePaginator#'
        - '#EloquentCollectionWrapper#'
        - '#BaseExceptionInterface#'
        - '#::expects#'
        - '#::method#'
        - '#::build#'

        # phpstan-banned-code
        - '#the non-test file#'
    excludePaths:
        analyse:
            - Modules/ThirdParty  # ThirdParty модуль может содержать не используемеъе вещи, котоъе храним на всякий случай
            - Modules/Core/Libraries/Scoring/StikScoring.php # Читай комменты в заголовке класса

    checkMissingIterableValueType: false

#    strictRules:
#        disallowedLooseComparison: false
#        booleansInConditions: false
#        uselessCast: false
#        requireParentConstructorCall: false
#        disallowedConstructs: false
#        overwriteVariablesWithLoop: false
#        closureUsesThis: false
#        matchingInheritedMethodNames: false
#        numericOperandsInArithmeticOperators: false
#        strictCalls: false
#        switchConditionsMatchingType: false
#        noVariableVariables: false
