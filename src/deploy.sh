#!/bin/bash

echo "git pull"
git pull

echo "php artisan migrate"
php artisan migrate

echo "php artisan cache:clear"
php artisan cache:clear

echo "php artisan config:clear"
php artisan config:clear

echo "php artisan route:clear"
php artisan route:clear

echo "php artisan view:clear"
php artisan view:clear

echo "php artisan script:permissions:register"
php artisan script:permissions:register

echo "supervisorctl restart all"
supervisorctl restart all

echo "npm install"
npm install

echo "npm run production"
npm run prod

echo "chmod -R 777 storage/logs"
chmod -R 777 storage/logs

echo "chown www-data:www-data -R storage/logs"
chown www-data:www-data -R storage/logs

echo "composer update stik-credit/calculators"
composer update stik-credit/calculators
