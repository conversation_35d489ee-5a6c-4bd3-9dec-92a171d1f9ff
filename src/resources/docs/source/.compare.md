---
title: API Reference

language_tabs:
- bash
- javascript

includes:

search: true

toc_footers:
- <a href='http://github.com/mpociot/documentarian'>Documentation Powered by Documentarian</a>
---
<!-- START_INFO -->
# Info

Welcome to the generated API reference.
[Get Postman Collection](http://localhost:8000/docs/collection.json)

<!-- END_INFO -->

#general


<!-- START_a9cf33d0519765bd4ab92faccd52edf0 -->
## api/v1
> Example request:

```bash
curl -X GET \
    -G "http://localhost:8000/api/v1?test_id=sit&test_id2=me&test_id3=4" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"test_id4":9,"test_id5":"cumque","test_id6":true}'

```

```javascript
const url = new URL(
    "http://localhost:8000/api/v1"
);

let params = {
    "test_id": "sit",
    "test_id2": "me",
    "test_id3": "4",
};
Object.keys(params)
    .forEach(key => url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "test_id4": 9,
    "test_id5": "cumque",
    "test_id6": true
}

fetch(url, {
    method: "GET",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "id": 4,
    "name": "Jessica Jones",
    "roles": [
        "admin"
    ]
}
```

### HTTP Request
`GET api/v1`

#### Query Parameters

Parameter | Status | Description
--------- | ------- | ------- | -----------
    `test_id` |  required  | The id of the location.
    `test_id2` |  required  | The id of the user.
    `test_id3` |  required  | The page number.
#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `test_id4` | integer |  required  | The id of the user.
        `test_id5` | string |  optional  | The id of the room.
        `test_id6` | boolean |  optional  | Whether to ban the user forever. Example:
    
<!-- END_a9cf33d0519765bd4ab92faccd52edf0 -->

<!-- START_05ec7ec3fd33272955524a4d7e0b683b -->
## api/v1/admin-test
> Example request:

```bash
curl -X GET \
    -G "http://localhost:8000/api/v1/admin-test?hello=dicta&hello2=me&hello3=4" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"hello4":9,"hello5":"occaecati","hello6":true}'

```

```javascript
const url = new URL(
    "http://localhost:8000/api/v1/admin-test"
);

let params = {
    "hello": "dicta",
    "hello2": "me",
    "hello3": "4",
};
Object.keys(params)
    .forEach(key => url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "hello4": 9,
    "hello5": "occaecati",
    "hello6": true
}

fetch(url, {
    method: "GET",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "id": 4,
    "name": "Jessica Jones",
    "roles": [
        "admin"
    ]
}
```

### HTTP Request
`GET api/v1/admin-test`

#### Query Parameters

Parameter | Status | Description
--------- | ------- | ------- | -----------
    `hello` |  required  | The id of the location.
    `hello2` |  required  | The id of the user.
    `hello3` |  required  | The page number.
#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `hello4` | integer |  required  | The id of the user.
        `hello5` | string |  optional  | The id of the room.
        `hello6` | boolean |  optional  | Whether to ban the user forever. Example:
    
<!-- END_05ec7ec3fd33272955524a4d7e0b683b -->

<!-- START_b06b25dda6374b9cd705090b0df09b9f -->
## api/v1/tmp-request
> Example request:

```bash
curl -X POST \
    "http://localhost:8000/api/v1/tmp-request" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "http://localhost:8000/api/v1/tmp-request"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "POST",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`POST api/v1/tmp-request`


<!-- END_b06b25dda6374b9cd705090b0df09b9f -->

<!-- START_1336f9a0501e4ace11ad26e53614db8b -->
## api/v2
> Example request:

```bash
curl -X GET \
    -G "http://localhost:8000/api/v2?location_id=quia&user_id=me&page=4" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"user_id":9,"room_id":"esse","forever":false}'

```

```javascript
const url = new URL(
    "http://localhost:8000/api/v2"
);

let params = {
    "location_id": "quia",
    "user_id": "me",
    "page": "4",
};
Object.keys(params)
    .forEach(key => url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "user_id": 9,
    "room_id": "esse",
    "forever": false
}

fetch(url, {
    method: "GET",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "id": 4,
    "name": "Jessica Jones",
    "roles": [
        "admin"
    ]
}
```

### HTTP Request
`GET api/v2`

#### Query Parameters

Parameter | Status | Description
--------- | ------- | ------- | -----------
    `location_id` |  required  | The id of the location.
    `user_id` |  required  | The id of the user.
    `page` |  required  | The page number.
#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `user_id` | integer |  required  | The id of the user.
        `room_id` | string |  optional  | The id of the room.
        `forever` | boolean |  optional  | Whether to ban the user forever.
    
<!-- END_1336f9a0501e4ace11ad26e53614db8b -->


