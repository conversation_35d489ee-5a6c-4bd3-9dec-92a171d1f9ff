/*
Copyright 2008-2013 Concur Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License"); you may
not use this file except in compliance with the License. You may obtain
a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
*/

////////////////////////////////////////////////////////////////////////////////
// CUSTOMIZE WHITEBOARD
//
// Use these settings to help adjust the appearance of Whiteboard
////////////////////////////////////////////////////////////////////////////////

////////////////////
// BACKGROUND COLORS
////////////////////
$nav-bg = #393939
$examples-bg = #393939
$code-bg = #292929
$code-annotation-bg = #1c1c1c
$nav-subitem-bg = #262626
$nav-active-bg = #2467af
$lang-select-border = black
$lang-select-bg = #222222

// feel free to change this to blue or something
$lang-select-active-bg = $examples-bg

// color of language tab bg when mouse is pressed
$lang-select-pressed-bg = #111111

$main-bg = #eaf2f6
$aside-notice-bg = #8fbcd4
$aside-warning-bg = #c97a7e
$aside-success-bg = #6ac174
$search-notice-bg = #c97a7e

////////////////////
// TEXT COLORS
////////////////////

// main content text color
$main-text = #333333

// color of unselected language tab text
$nav-text = white
$nav-active-text = white
$lang-select-text = white

// color of selected language tab text
$lang-select-active-text = white

// color of language tab text when mouse is pressed
$lang-select-pressed-text = white

////////////////////
// SIZES
////////////////////

// width of the navbar
$nav-width = 230px

// portion of the screen taken up by code examples
$examples-width = 50%

// margin between nav items and logo, ignored if search is active
$logo-margin = 20px

// padding to left and right of content & examples
$main-padding = 28px

// padding to left and right of navbar
$nav-padding = 15px

// padding used vertically around search boxes and results
$nav-v-padding = 10px

// extra padding for ToC subitems
$nav-indent = 10px

// padding inside code annotations
$code-annotation-padding = 13px

// padding under the largest header tags
$h1-margin-bottom = 21px

// min width before reverting to tablet size
$tablet-width = 930px

// min width before reverting to mobile size
$phone-width = $tablet-width - $nav-width

////////////////////
// FONTS
////////////////////

$default-font
  font-family: "Helvetica Neue", Helvetica, Arial, "Microsoft Yahei","微软雅黑", STXihei, "华文细黑", sans-serif
  font-size: 13px

$header-font
  @extend $default-font
  font-weight: bold

$code-font
  font-family: Consolas, Menlo, Monaco, "Lucida Console", "Liberation Mono", "DejaVu Sans Mono", "Bitstream Vera Sans Mono", "Courier New", monospace, serif
  font-size: 12px
  line-height: 1.5

$font-icon = FontAwesome
$font-icon-path = "fonts/fontawesome-webfont"
$font-icon-version = "4.0.3"

////////////////////
// OTHER
////////////////////

$nav-active-shadow = black
$nav-footer-border-color = #666666
$nav-embossed-border-top = black
$nav-embossed-border-bottom = #939393
$main-embossed-text-shadow = 0px 1px 0px white
$search-box-border-color = #666666

////////////////////////////////////////////////////////////////////////////////
// INTERNAL
////////////////////////////////////////////////////////////////////////////////
// These settings are probably best left alone.
$break-words
  word-break: break-all
  /* Non standard for webkit */
  word-break: break-word
  hyphens: auto
