<?php


return [
    'Cash payment method is incompatible with online loan' => 'Не може да се избере плащане в брой за офис Интернет',
    'Client address not saved' => 'Грешка при запазване на адреса на клиент',
    'Client name not saved' => 'Грешка при създаване на имена на клиент',
    'Client not saved' => 'Грешка при запазване на клиент',
    'Contact not saved' => 'Грешка при запазване на лице за контакт',
    'Contact has no phone and no email' => 'Лицето за контакт няма въведен телефон',
    'discount of :discount is above limit of :limit' => 'Грешка при запазване на отстъпка. Въведена е по-голяма от лимита за този администратор',
    'EasyPay method is incompatible with taking loan in local office' => 'Не може да се избере плащане по изипей за офис',
    'Email missing from online loan' => 'Трябва да се посочи емейл адрес на клиента',
    'Guarantor has no pin' => 'Поръчителя няма въведено ЕГН',
    'Guarantor not saved' => 'Грешка при запазване на поръчител.',
    'Client ID card has no expiration date or expired' => 'Не е попълнена дата на валидност на ЛК или ЛК е изтекла',
    'bank with id :bankId was not found' => 'Грешка при вземане на банк акаунт ИД',
    'incorrect payment method ID: (:methodId)' => 'Методът за плащане не съществува',
    'Client guarantor not saved' => 'Грешка при запазване на релация кредит - поръчител',
    'Loan not saved' => 'Грешка при запазване на заема',
    'Office: (:officeId) does not have  product: (:productId)' => 'Офисът няма достъп до този продукт',
];
