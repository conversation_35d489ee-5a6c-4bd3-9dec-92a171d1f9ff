<?php

return array(
    'accepted' => ':attribute трябва да бъде приет.',
    'active_url' => ':attribute не е валиден URL адрес.',
    'after' => ':attribute трябва да бъде дата след :date.',
    'after_or_equal' => ':attribute трябва да бъде дата след или равна на :date.',
    'alpha' => ':attribute може да съдържа само букви.',
    'alpha_dash' => ':attribute може да съдържа само букви, цифри, тирета и подчертавки.',
    'alpha_num' => ':attribute може да съдържа само букви и цифри.',
    'array' => ':attribute трябва да бъде масив.',
    'before' => ':attribute трябва да бъде дата преди :date.',
    'before_or_equal' => ':attribute трябва да бъде дата преди или равна на :date.',
    'between' =>
        array(
            'numeric' => ':attribute трябва да бъде между :min и :max.',
            'file' => ':attribute трябва да бъде между :min и :max килобайта.',
            'string' => ':attribute трябва да бъде между :min и :max символа.',
            'array' => ':attribute трябва да има между :min и :max елемента.',
        ),
    'boolean' => 'Полето :attribute трябва да бъде истина или лъжа.',
    'confirmed' => 'Потвърждението на :attribute не съвпада.',
    'date' => ':attribute не е валидна дата.',
    'date_equals' => ':attribute трябва да бъде дата, равна на :date.',
    'date_format' => ':attribute не съвпада с формата :format.',
    'different' => ':attribute и :other трябва да бъдат различни.',
    'digits' => ':attribute трябва да бъде :digits цифри.',
    'digits_between' => ':attribute трябва да бъде между :min и :max цифри.',
    'dimensions' => ':attribute има невалидни размери на изображението.',
    'distinct' => 'Полето :attribute има дублирана стойност.',
    'email' => ':attribute трябва да бъде валиден имейл адрес.',
    'ends_with' => ':attribute трябва да завършва с едно от следните: :values.',
    'exists' => 'Избраното :attribute е невалидно.',
    'file' => ':attribute трябва да бъде файл.',
    'filled' => 'Полето :attribute трябва да има стойност.',
    'gt' =>
        array(
            'numeric' => ':attribute трябва да бъде по-голямо от :value.',
            'file' => ':attribute трябва да бъде по-голямо от :value килобайта.',
            'string' => ':attribute трябва да бъде по-голямо от :value символа.',
            'array' => ':attribute трябва да има повече от :value елемента.',
        ),
    'gte' =>
        array(
            'numeric' => ':attribute трябва да бъде по-голямо или равно на :value.',
            'file' => ':attribute трябва да бъде по-голямо или равно на :value килобайта.',
            'string' => ':attribute трябва да бъде по-голямо или равно на :value символа.',
            'array' => ':attribute трябва да има :value елемента или повече.',
        ),
    'image' => ':attribute трябва да бъде изображение.',
    'in' => 'Избраното :attribute е невалидно.',
    'in_array' => 'Полето :attribute не съществува в :other.',
    'integer' => ':attribute трябва да бъде цяло число.',
    'ip' => ':attribute трябва да бъде валиден IP адрес.',
    'ipv4' => ':attribute трябва да бъде валиден IPv4 адрес.',
    'ipv6' => ':attribute трябва да бъде валиден IPv6 адрес.',
    'json' => ':attribute трябва да бъде валиден JSON низ.',
    'lt' =>
        array(
            'numeric' => ':attribute трябва да бъде по-малко от :value.',
            'file' => ':attribute трябва да бъде по-малко от :value килобайта.',
            'string' => ':attribute трябва да бъде по-малко от :value символа.',
            'array' => ':attribute трябва да има по-малко от :value елемента.',
        ),
    'lte' =>
        array(
            'numeric' => ':attribute трябва да бъде по-малко или равно на :value.',
            'file' => ':attribute трябва да бъде по-малко или равно на :value килобайта.',
            'string' => ':attribute трябва да бъде по-малко или равно на :value символа.',
            'array' => ':attribute не трябва да има повече от :value елемента.',
        ),
    'max' =>
        array(
            'numeric' => ':attribute не може да бъде по-голямо от :max.',
            'file' => ':attribute не може да бъде по-голямо от :max килобайта.',
            'string' => ':attribute не може да бъде по-голямо от :max символа.',
            'array' => ':attribute не може да има повече от :max елемента.',
        ),
    'mimes' => ':attribute трябва да бъде файл от тип: :values.',
    'mimetypes' => ':attribute трябва да бъде файл от тип: :values.',
    'min' =>
        array(
            'numeric' => ':attribute трябва да бъде поне :min.',
            'file' => ':attribute трябва да бъде поне :min килобайта.',
            'string' => ':attribute трябва да бъде поне :min символа.',
            'array' => ':attribute трябва да има поне :min елемента.',
        ),
    'not_in' => 'Избраното :attribute е невалидно.',
    'not_regex' => 'Форматът на :attribute е невалиден.',
    'numeric' => ':attribute трябва да бъде число.',
    'password' => 'Паролата е неправилна.',
    'present' => 'Полето :attribute трябва да бъде представено.',
    'regex' => 'Форматът на :attribute е невалиден.',
    'required' => 'Полето :attribute е задължително.',
    'required_if' => 'Полето :attribute е задължително, когато :other е :value.',
    'required_unless' => 'Полето :attribute е задължително, освен ако :other не е в :values.',
    'required_with' => 'Полето :attribute е задължително, когато :values е представено.',
    'required_with_all' => 'Полето :attribute е задължително, когато :values са представени.',
    'required_without' => 'Полето :attribute е задължително, когато :values не е представено.',
    'required_without_all' => 'Полето :attribute е задължително, когато нито едно от :values не са представени.',
    'same' => ':attribute и :other трябва да съвпадат.',
    'size' =>
        array(
            'numeric' => ':attribute трябва да бъде :size.',
            'file' => ':attribute трябва да бъде :size килобайта.',
            'string' => ':attribute трябва да бъде :size символа.',
            'array' => ':attribute трябва да съдържа :size елемента.',
        ),
    'starts_with' => ':attribute трябва да започва с едно от следните: :values.',
    'string' => ':attribute трябва да бъде низ.',
    'timezone' => ':attribute трябва да бъде валидна зона.',
    'unique' => ':attribute вече е заето.',
    'uploaded' => ':attribute не успя да се качи.',
    'url' => 'Форматът на :attribute е невалиден.',
    'uuid' => ':attribute трябва да бъде валиден UUID.',
    'is_valid_pin' => ':attribute е невалиден.',
    'custom' =>
        array(
            'attribute-name' =>
                array(
                    'rule-name' => 'персонализирано съобщение',
                ),
        ),
    'min_digits' => ':attribute трябва да има поне :min цифри.',
    'max_digits' => ':attribute не трябва да има повече от :max цифри.',
);
?>
