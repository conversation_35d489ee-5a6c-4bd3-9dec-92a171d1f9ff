import list from './list'
import otherNotify from './notify'

axios.interceptors.response.use(null, function (error) {

    // if (!error.response) {
    //     return Promise.reject('handle from axios:' + error)
    // } else if (error.response.status === 401) {
    //     otherNotify.responseError(error.response.status, 'Трябва се логнете наново', '')
    // } else if (error.response.status === 419) {
    //     otherNotify.responseError(error.response.status, 'Сесията Ви е изтекла. Рестартирайте страница или се логнете на ново ', '')
    // } else {
    //     otherNotify.responseError(error.response.status, 'Обърнете се към IT отдел', '')
    // }

    console.log('maika mu da eba, putka mu da eba', error);
    return Promise.reject(error);
})

/**
 * @param {routeEnum} name
 * @param replacements
 * @param data
 * @param params
 * @param config
 * @returns {null|Promise<AxiosResponse<any>>}
 */
export default function (name, replacements = {}, data = {}, params = {}, config = {}) {
    if (window.axiosStop) {
        return Promise.reject()
    }
    if (!list.hasOwnProperty(name)) {
        console.warn('route "' + name + '" is undefined')
        return Promise.reject()
    }

    const routeItem = list[name][1]
    const url = routeItem.replace(/{(\w+)}/g, (match, routeKey) => {
        if (!replacements.hasOwnProperty(routeKey)) {
            throw new Error('"' + routeKey + '" in replacements route "' + name + '" is undefined')
        }
        return replacements[routeKey]
    })

    let method = list[name][0].toLowerCase()
    if (method === 'get') {
        params = Object.assign(data, params)
        data = {}
    }
    if (method === 'put') {
        method = 'post'
        params = Object.assign({_method: 'put'}, params)
    }
    if (method === 'delete') {
        method = 'post'
        params = Object.assign({_method: 'delete'}, params)
    }
    const configRequest = Object.assign(config, {method, url, params, data})

    return axios.request(configRequest)
}
