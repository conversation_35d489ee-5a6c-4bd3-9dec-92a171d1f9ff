export default {
    "home": 		["GET","/"],
    "set-pagination-limit": 		["GET","/admin/set-pagination-limit/{forPage}/{limit}"],
    "admin.administrators.list": 		["GET","/admin/administrators"],
    "admin.administrators.getFilters": 		["GET","/admin/administrators/filters"],
    "admin.administrators.setFilters": 		["PUT","/admin/administrators/filters"],
    "admin.administrators.cleanFilters": 		["DELETE","/admin/administrators/filters"],
    "admin.administrators.refresh": 		["GET","/admin/administrators/refresh"],
    "admin.administrators.create": 		["GET","/admin/administrators/create"],
    "admin.administrators.store": 		["POST","/admin/administrators/store"],
    "admin.administrators.edit": 		["GET","/admin/administrators/edit/{admin}"],
    "admin.administrators.update": 		["POST","/admin/administrators/update/{admin}"],
    "admin.administrators.delete": 		["GET","/admin/administrators/delete/{admin}"],
    "admin.administrators.enable": 		["GET","/admin/administrators/enable/{admin}"],
    "admin.administrators.disable": 		["GET","/admin/administrators/disable/{admin}"],
    "admin.agreements.refresh": 		["GET","/admin/agreements/refresh"],
    "admin.agreements.getFilters": 		["GET","/admin/agreements/filters"],
    "admin.agreements.setFilters": 		["PUT","/admin/agreements/filters"],
    "admin.agreements.cleanFilters": 		["DELETE","/admin/agreements/filters"],
    "admin.agreements.list": 		["GET","/admin/agreements"],
    "admin.agreements.create": 		["GET","/admin/agreements/create"],
    "admin.agreements.store": 		["POST","/admin/agreements/store"],
    "admin.agreements.edit": 		["GET","/admin/agreements/edit/{agreement}"],
    "admin.agreements.update": 		["POST","/admin/agreements/update/{agreement}"],
    "admin.agreements.delete": 		["GET","/admin/agreements/delete/{agreement}"],
    "admin.agreements.enable": 		["GET","/admin/agreements/enable/{agreement}"],
    "admin.agreements.disable": 		["GET","/admin/agreements/disable/{agreement}"],
    "admin.branches.list": 		["GET","/admin/branches"],
    "admin.branches.create": 		["GET","/admin/branches/create"],
    "admin.branches.store": 		["POST","/admin/branches/store"],
    "admin.branches.edit": 		["GET","/admin/branches/edit/{branch}"],
    "admin.branches.update": 		["POST","/admin/branches/update/{branch}"],
    "admin.branches.delete": 		["GET","/admin/branches/delete/{branch}"],
    "admin.branches.enable": 		["GET","/admin/branches/enable/{branch}"],
    "admin.branches.disable": 		["GET","/admin/branches/disable/{branch}"],
    "admin.branches.refresh": 		["GET","/admin/branches/refresh"],
    "admin.branches.getFilters": 		["GET","/admin/branches/filters"],
    "admin.branches.setFilters": 		["PUT","/admin/branches/filters"],
    "admin.branches.cleanFilters": 		["DELETE","/admin/branches/filters"],
    "admin.close-reasons.refresh": 		["GET","/admin/close-reasons/refresh"],
    "admin.close-reasons.getFilters": 		["GET","/admin/close-reasons/filters"],
    "admin.close-reasons.setFilters": 		["PUT","/admin/close-reasons/filters"],
    "admin.close-reasons.cleanFilters": 		["DELETE","/admin/close-reasons/filters"],
    "admin.close-reasons.list": 		["GET","/admin/close-reasons"],
    "admin.close-reasons.create": 		["GET","/admin/close-reasons/create"],
    "admin.close-reasons.store": 		["POST","/admin/close-reasons/store"],
    "admin.close-reasons.edit": 		["GET","/admin/close-reasons/edit/{closeReason}"],
    "admin.close-reasons.update": 		["POST","/admin/close-reasons/update/{closeReason}"],
    "admin.close-reasons.delete": 		["GET","/admin/close-reasons/delete/{closeReason}"],
    "admin.close-reasons.enable": 		["GET","/admin/close-reasons/enable/{closeReason}"],
    "admin.close-reasons.disable": 		["GET","/admin/close-reasons/disable/{closeReason}"],
    "admin.landing.sections": 		["GET","/admin/landing-sections"],
    "admin.landing.sectionsCreate": 		["GET","/admin/landing-sections-create"],
    "admin.landing.sectionsEdit": 		["GET","/admin/landing-sections-edit/{landingSectionId}"],
    "admin.landing.sectionsCreateSubmit": 		["POST","/admin/landing-sections-create-submit"],
    "admin.landing.sectionsDisableEnable": 		["GET","/admin/landing-sections-disable-enable/{landingSectionId}"],
    "admin.landing.sectionsPriorityUpDown": 		["GET","/admin/landing-sections-priority-up-down/{landingSectionId}"],
    "admin.landing.docs": 		["GET","/admin/landing-docs"],
    "admin.landing.docsCreate": 		["GET","/admin/landing-docs-create"],
    "admin.landing.docEdit": 		["GET","/admin/landing-docs-edit/{landingDocId}"],
    "admin.landing.docsCreateSubmit": 		["POST","/admin/landing-docs-create-submit"],
    "admin.landing.docDisableEnable": 		["GET","/admin/landing-docs-disable-enable/{landingDocId}"],
    "admin.landing.docsList": 		["PUT","/admin/landing-docs/filters"],
    "admin.offices.fetchOfficeAdministrators": 		["GET","/admin/offices/fetch-office-administrators/{office}"],
    "admin.offices.getFilters": 		["GET","/admin/offices/filters"],
    "admin.offices.setFilters": 		["PUT","/admin/offices/filters"],
    "admin.offices.cleanFilters": 		["DELETE","/admin/offices/filters"],
    "admin.offices.refresh": 		["GET","/admin/offices/refresh"],
    "admin.offices.list": 		["GET","/admin/offices"],
    "admin.offices.create": 		["GET","/admin/offices/create"],
    "admin.offices.store": 		["POST","/admin/offices/store"],
    "admin.offices.edit": 		["GET","/admin/offices/edit/{office}"],
    "admin.offices.update": 		["POST","/admin/offices/update/{office}"],
    "admin.offices.delete": 		["GET","/admin/offices/delete/{office}"],
    "admin.offices.enable": 		["GET","/admin/offices/enable/{office}"],
    "admin.offices.disable": 		["GET","/admin/offices/disable/{office}"],
    "admin.roles.getFilters": 		["GET","/admin/roles/filters"],
    "admin.roles.setFilters": 		["PUT","/admin/roles/filters"],
    "admin.roles.cleanFilters": 		["DELETE","/admin/roles/filters"],
    "admin.roles.refresh": 		["GET","/admin/roles/refresh"],
    "admin.roles.list": 		["GET","/admin/roles"],
    "admin.roles.create": 		["GET","/admin/roles/create"],
    "admin.roles.store": 		["POST","/admin/roles/store"],
    "admin.roles.edit": 		["GET","/admin/roles/edit/{role}"],
    "admin.roles.update": 		["POST","/admin/roles/update/{role}"],
    "admin.roles.delete": 		["GET","/admin/roles/delete/{role}"],
    "admin.roles.enable": 		["GET","/admin/roles/enable/{role}"],
    "admin.roles.disable": 		["GET","/admin/roles/disable/{role}"],
    "admin.setting.types.list": 		["GET","/admin/setting-types"],
    "admin.setting.types.create": 		["GET","/admin/setting-types/create"],
    "admin.setting.types.store": 		["POST","/admin/setting-types/store"],
    "admin.setting.types.edit": 		["GET","/admin/setting-types/edit/{settingType}"],
    "admin.setting.types.update": 		["POST","/admin/setting-types/update/{settingType}"],
    "admin.setting.types.delete": 		["GET","/admin/setting-types/delete/{settingType}"],
    "admin.setting.types.enable": 		["GET","/admin/setting-types/enable/{settingType}"],
    "admin.setting.types.disable": 		["GET","/admin/setting-types/disable/{settingType}"],
    "admin.settings.list": 		["GET","/admin/settings"],
    "admin.settings.create": 		["GET","/admin/settings/create"],
    "admin.settings.store": 		["POST","/admin/settings/store"],
    "admin.settings.edit": 		["GET","/admin/settings/edit/{setting}"],
    "admin.settings.update": 		["POST","/admin/settings/update/{setting}"],
    "admin.settings.delete": 		["GET","/admin/settings/delete/{setting}"],
    "admin.settings.enable": 		["GET","/admin/settings/enable/{setting}"],
    "admin.settings.disable": 		["GET","/admin/settings/disable/{setting}"],
    "admin.settings.getFilters": 		["GET","/admin/settings/filters"],
    "admin.settings.setFilters": 		["PUT","/admin/settings/filters"],
    "admin.settings.cleanFilters": 		["DELETE","/admin/settings/filters"],
    "admin.settings.refresh": 		["GET","/admin/settings/refresh"],
    "admin.tmp-request-steps.refresh": 		["GET","/admin/tmp-request-steps/refresh"],
    "admin.tmp-request-steps.getFilters": 		["GET","/admin/tmp-request-steps/filters"],
    "admin.tmp-request-steps.setFilters": 		["PUT","/admin/tmp-request-steps/filters"],
    "admin.tmp-request-steps.cleanFilters": 		["DELETE","/admin/tmp-request-steps/filters"],
    "admin.tmp-request-steps.list": 		["GET","/admin/tmp-request-steps"],
    "admin.tmp-request-steps.create": 		["GET","/admin/tmp-request-steps/create"],
    "admin.tmp-request-steps.store": 		["POST","/admin/tmp-request-steps/store"],
    "admin.tmp-request-steps.edit": 		["GET","/admin/tmp-request-steps/edit/{tmpRequestStep}"],
    "admin.tmp-request-steps.update": 		["POST","/admin/tmp-request-steps/update/{tmpRequestStep}"],
    "admin.tmp-request-steps.delete": 		["GET","/admin/tmp-request-steps/delete/{tmpRequestStep}"],
    "admin.tmp-request-steps.enable": 		["GET","/admin/tmp-request-steps/enable/{tmpRequestStep}"],
    "admin.tmp-request-steps.disable": 		["GET","/admin/tmp-request-steps/disable/{tmpRequestStep}"],
    "admin.fiscal-devices.create": 		["POST","/admin/fiscal-device/create"],
    "admin.fiscal-devices.edit": 		["POST","/admin/fiscal-device/update/{fiscalDevice}"],
    "admin.fiscal-devices.delete": 		["DELETE","/admin/fiscal-device/delete/{fiscalDevice}"],
    "approve.loan-decision.approve": 		["POST","/approve/loan-decision/approve"],
    "approve.loan-decision.cancel": 		["POST","/approve/loan-decision/cancel"],
    "approve.loan-decision.delay": 		["POST","/approve/loan-decision/delay"],
    "approve.loan-decision.process": 		["GET","/approve/loan-decision/process/{loan}"],
    "approve.approveDecision.list": 		["GET","/approve/approve-decision"],
    "approve.approveDecision.create": 		["GET","/approve/approve-decision/create"],
    "approve.approveDecision.store": 		["POST","/approve/approve-decision/store"],
    "approve.approveDecision.edit": 		["GET","/approve/approve-decision/edit/{approveDecision}"],
    "approve.approveDecision.update": 		["POST","/approve/approve-decision/update/{approveDecision}"],
    "approve.approveDecision.delete": 		["GET","/approve/approve-decision/delete/{approveDecision}"],
    "approve.approveDecision.enable": 		["GET","/approve/approve-decision/enable/{approveDecision}"],
    "approve.approveDecision.disable": 		["GET","/approve/approve-decision/disable/{approveDecision}"],
    "approve.approveDecisionReason.list": 		["GET","/approve/approve-decision-reason"],
    "approve.approveDecisionReason.create": 		["GET","/approve/approve-decision-reason/create"],
    "approve.approveDecisionReason.store": 		["POST","/approve/approve-decision-reason/store"],
    "approve.approveDecisionReason.edit": 		["GET","/approve/approve-decision-reason/edit/{approveDecisionReason}"],
    "approve.approveDecisionReason.update": 		["POST","/approve/approve-decision-reason/update/{approveDecisionReason}"],
    "approve.approveDecisionReason.delete": 		["GET","/approve/approve-decision-reason/delete/{approveDecisionReason}"],
    "approve.approveDecisionReason.enable": 		["GET","/approve/approve-decision-reason/enable/{approveDecisionReason}"],
    "approve.approveDecisionReason.disable": 		["GET","/approve/approve-decision-reason/disable/{approveDecisionReason}"],
    "payment.cashDesk.selectOffice": 		["GET","/cash-desk/cash-desk/cash-desk"],
    "payment.cashDesk.topPanel": 		["GET","/cash-desk/cash-desk/topPanel/{office}"],
    "payment.cashDesk.list": 		["GET","/cash-desk/cash-desk/office/{office}"],
    "payment.cashDesk.refresh": 		["GET","/cash-desk/cash-desk/refresh/{office}"],
    "payment.cashDesk.tryAgain": 		["POST","/cash-desk/cash-desk/try-again"],
    "payment.cashDesk.clearTremolSession": 		["DELETE","/cash-desk/cash-desk/clear-tremol-session"],
    "payment.cashDesk.getModal": 		["GET","/cash-desk/cash-desk/modal"],
    "payment.cashDesk.getInitBalanceModal": 		["GET","/cash-desk/cash-desk/initBalanceModal"],
    "payment.cashDesk.edit": 		["GET","/cash-desk/cash-desk/edit/{id}"],
    "payment.cashDesk.add": 		["POST","/cash-desk/cash-desk/add"],
    "payment.cashDesk.initBalance": 		["POST","/cash-desk/cash-desk/initBalance"],
    "payment.cashDesk.update": 		["POST","/cash-desk/cash-desk/update/{id}"],
    "payment.cashDesk.export": 		["POST","/cash-desk/cash-desk/export/{office}"],
    "payment.cashDesk.download": 		["GET","/cash-desk/cash-desk/download/{id}"],
    "payment.cashDesk.getDocument": 		["GET","/cash-desk/cash-desk/document/{cashOperationalDocument}"],
    "payment.cashDesk.taxFeeTransaction": 		["POST","/cash-desk/cash-desk/tax-fee"],
    "terminal-log.index": 		["GET","/terminal-log"],
    "terminal-log.show": 		["GET","/terminal-log/show/{terminalLog}"],
    "terminal-log.confirmation": 		["POST","/terminal-log/confirmation/{terminalLog}"],
    "terminal-log.resend": 		["GET","/terminal-log/resend/{terminalLog}"],
    "terminal-log.test": 		["GET","/terminal-log/test"],
    "collect.collector-decisions.list": 		["GET","/collect/collector-decisions"],
    "collect.collector-decisions.create": 		["GET","/collect/collector-decisions/create"],
    "collect.collector-decisions.store": 		["POST","/collect/collector-decisions/store"],
    "collect.collector-decisions.edit": 		["GET","/collect/collector-decisions/edit/{collectorDecision}"],
    "collect.collector-decisions.update": 		["POST","/collect/collector-decisions/update/{collectorDecision}"],
    "collect.collector-decisions.delete": 		["GET","/collect/collector-decisions/delete/{collectorDecision}"],
    "collect.collector-decisions.enable": 		["GET","/collect/collector-decisions/enable/{collectorDecision}"],
    "collect.collector-decisions.disable": 		["GET","/collect/collector-decisions/disable/{collectorDecision}"],
    "collect.collector-decisions.refresh": 		["GET","/collect/collector-decisions/refresh"],
    "collect.collector-decisions.getFilters": 		["GET","/collect/collector-decisions/filters"],
    "collect.collector-decisions.setFilters": 		["PUT","/collect/collector-decisions/filters"],
    "collect.collector-decisions.cleanFilters": 		["DELETE","/collect/collector-decisions/filters"],
    "collect.buckets.list": 		["GET","/collect/buckets"],
    "collect.buckets.create": 		["GET","/collect/buckets/create"],
    "collect.buckets.store": 		["POST","/collect/buckets/store"],
    "collect.buckets.edit": 		["GET","/collect/buckets/edit/{bucket}"],
    "collect.buckets.update": 		["POST","/collect/buckets/update/{bucket_id}"],
    "collect.buckets.delete": 		["GET","/collect/buckets/delete/{bucket}"],
    "collect.buckets.enable": 		["GET","/collect/buckets/enable/{bucket}"],
    "collect.buckets.disable": 		["GET","/collect/buckets/disable/{bucket}"],
    "collect.buckets.refresh": 		["GET","/collect/buckets/refresh"],
    "collect.buckets.getFilters": 		["GET","/collect/buckets/filters"],
    "collect.buckets.setFilters": 		["PUT","/collect/buckets/filters"],
    "collect.buckets.cleanFilters": 		["DELETE","/collect/buckets/filters"],
    "collect.bucket-tasks.list": 		["GET","/collect/bucket-tasks"],
    "collect.bucket-tasks.refresh": 		["GET","/collect/bucket-tasks/refresh"],
    "collect.bucket-tasks.getFilters": 		["GET","/collect/bucket-tasks/filters"],
    "collect.bucket-tasks.setFilters": 		["PUT","/collect/bucket-tasks/filters"],
    "collect.bucket-tasks.cleanFilters": 		["DELETE","/collect/bucket-tasks/filters"],
    "collect.bucket-tasks.process": 		["GET","/collect/bucket-tasks/process/{bucketTask}"],
    "collect.collector-attempt.create": 		["POST","/collect/bucket-tasks/collector-attempt"],
    "collect.mass-collector-attempt.create": 		["POST","/collect/bucket-tasks/mass-collector-attempt"],
    "collect.legal-docs.choose": 		["GET","/collect/bucket-tasks/legal-docs-by-choice"],
    "collect.legal-docs.filter": 		["GET","/collect/bucket-tasks/legal-docs-by-filter"],
    "collect.loan-buckets.list": 		["GET","/collect/loan-buckets"],
    "collect.loan-buckets.store": 		["GET","/collect/loan-buckets/store"],
    "common.payment-methods.options-select": 		["GET","/common/payment-methods/select-options/{office}"],
    "common.slider.fetch-product-settings": 		["GET","/common/fetch-product-settings"],
    "common.slider.calculateLoan": 		["GET","/common/calculate-loan"],
    "common.loans.cities": 		["GET","/common/cities"],
    "common.loans.city": 		["GET","/common/city/{city}"],
    "common.loans.getCityName": 		["GET","/common/cityName/{city}"],
    "common.client-card-boxes.index": 		["GET","/common/client-card-boxes"],
    "common.client-card-boxes.edit": 		["GET","/common/client-card-boxes/{client_card_box}/edit"],
    "common.client-card-boxes.update-box": 		["POST","/common/client-card-boxes/{client_card_box}"],
    "common.client-card-boxes.save-box-order": 		["GET","/common/client-card-boxes/save-box-order"],
    "communication.clientCardCommunication.sendEarlyCommunication": 		["GET","/communication/client-card-communication"],
    "communication.email.list": 		["GET","/communication/email"],
    "communication.email.preview": 		["GET","/communication/email/{email}/preview"],
    "communication.email.sendEmail": 		["POST","/communication/email/sendEmail"],
    "communication.emailTemplate.list": 		["GET","/communication/email-templates"],
    "communication.emailTemplate.create": 		["GET","/communication/email-template/create"],
    "communication.emailTemplate.store": 		["POST","/communication/email-template/store"],
    "communication.emailTemplate.edit": 		["GET","/communication/email-template/edit/{emailTemplate}"],
    "communication.emailTemplate.update": 		["POST","/communication/email-template/update/{emailTemplate}"],
    "communication.emailTemplate.delete": 		["GET","/communication/email-template/delete/{emailTemplate}"],
    "communication.emailTemplate.enable": 		["GET","/communication/email-template/enable/{emailTemplate}"],
    "communication.emailTemplate.disable": 		["GET","/communication/email-template/disable/{emailTemplate}"],
    "communication.emailTemplate.revert": 		["GET","/communication/email-template/revert/{emailTemplate}/{logEmailTemplate}"],
    "communication.emailTemplate.getFilters": 		["GET","/communication/email-template/filters"],
    "communication.emailTemplate.setFilters": 		["PUT","/communication/email-template/filters"],
    "communication.emailTemplate.cleanFilters": 		["DELETE","/communication/email-template/filters"],
    "communication.emailTemplate.refresh": 		["GET","/communication/email-template/refresh"],
    "communication.sms.list": 		["GET","/communication/sms"],
    "communication.sms.sendSms": 		["POST","/communication/sms/sendSms"],
    "communication.smsTemplate.list": 		["GET","/communication/sms-templates"],
    "communication.smsTemplate.create": 		["GET","/communication/sms-template/create"],
    "communication.smsTemplate.store": 		["POST","/communication/sms-template/store"],
    "communication.smsTemplate.edit": 		["GET","/communication/sms-template/edit/{smsTemplate}"],
    "communication.smsTemplate.update": 		["POST","/communication/sms-template/update/{smsTemplate}"],
    "communication.smsTemplate.delete": 		["GET","/communication/sms-template/delete/{smsTemplate}"],
    "communication.smsTemplate.enable": 		["GET","/communication/sms-template/enable/{smsTemplate}"],
    "communication.smsTemplate.disable": 		["GET","/communication/sms-template/disable/{smsTemplate}"],
    "communication.smsTemplate.revert": 		["GET","/communication/sms-template/revert/{smsTemplate}/{logSmsTemplate}"],
    "communication.smsTemplate.getFilters": 		["GET","/communication/sms-template/filters"],
    "communication.smsTemplate.setFilters": 		["PUT","/communication/sms-template/filters"],
    "communication.smsTemplate.cleanFilters": 		["DELETE","/communication/sms-template/filters"],
    "communication.smsTemplate.refresh": 		["GET","/communication/sms-template/refresh"],
    "communication.viber.list": 		["GET","/communication/viber"],
    "communication.viber.send": 		["POST","/communication/viber/sendViberMessage"],
    "communication.viber.getFilters": 		["GET","/communication/viber/filters"],
    "communication.viber.setFilters": 		["PUT","/communication/viber/filters"],
    "communication.viber.cleanFilters": 		["DELETE","/communication/viber/filters"],
    "communication.viber.refresh": 		["GET","/communication/viber/refresh"],
    "communication.viberTemplate.list": 		["GET","/communication/viber-templates"],
    "communication.viberTemplate.create": 		["GET","/communication/viber-template/create"],
    "communication.viberTemplate.store": 		["POST","/communication/viber-template/store"],
    "communication.viberTemplate.edit": 		["GET","/communication/viber-template/edit/{viberTemplate}"],
    "communication.viberTemplate.update": 		["POST","/communication/viber-template/update/{viberTemplate}"],
    "communication.viberTemplate.delete": 		["GET","/communication/viber-template/delete/{viberTemplate}"],
    "communication.viberTemplate.enable": 		["GET","/communication/viber-template/enable/{viberTemplate}"],
    "communication.viberTemplate.disable": 		["GET","/communication/viber-template/disable/{viberTemplate}"],
    "communication.viberTemplate.revert": 		["GET","/communication/viber-template/revert/{viberTemplate}/{logViberTemplate}"],
    "communication.viberTemplate.getFilters": 		["GET","/communication/viber-template/filters"],
    "communication.viberTemplate.setFilters": 		["PUT","/communication/viber-template/filters"],
    "communication.viberTemplate.cleanFilters": 		["DELETE","/communication/viber-template/filters"],
    "communication.viberTemplate.refresh": 		["GET","/communication/viber-template/refresh"],
    "communication.notificationSetting.edit": 		["POST","/communication/notification-setting/edit"],
    "communication.notificationSetting.communicationTabEdit": 		["POST","/communication/notification-setting/edit-communication-tab/{notificationSetting}"],
    "communication.notification-setting.updateClientNotificationSettings": 		["POST","/communication/notification-setting/update-client-settings"],
    "communication.communicationComment.createCommunicationComment": 		["POST","/communication/communication-comment/create/{client}"],
    "communication.template.preview": 		["POST","/communication/template/preview/{client}/{loan?}"],
    "communication.template.download": 		["GET","/communication/template/download/{client}/{loan?}"],
    "communication.template.send": 		["POST","/communication/template/send/{client}/{loan?}"],
    "head.discountsClients.list": 		["GET","/discounts-clients"],
    "head.clients.removeDiscount": 		["DELETE","/clients/remove-discount/{id}"],
    "head.clients.massRemoveDiscounts": 		["POST","/clients/mass-remove-discount"],
    "head.clients.refreshDiscounts": 		["GET","/clients/refresh-discounts"],
    "head.clients.setDiscountsFilters": 		["PUT","/clients/filters-discounts"],
    "head.clients.getDiscountsFilters": 		["GET","/clients/filters-discounts"],
    "head.clients.cleanDiscountsFilters": 		["DELETE","/clients/filters-discounts"],
    "head.clients.importClientsDiscount": 		["POST","/clients/import-client-discount-actual"],
    "head.clients.importClientsDiscountManual": 		["POST","/clients/import-client-discount-manual"],
    "head.discountsClients.search": 		["GET","/discounts-clients-search"],
    "docs.documentTemplate.list": 		["GET","/docs/document-templates"],
    "docs.documentTemplate.create": 		["GET","/docs/document-template/create"],
    "docs.documentTemplate.store": 		["POST","/docs/document-template/store"],
    "docs.documentTemplate.edit": 		["GET","/docs/document-template/edit/{documentTemplate}"],
    "docs.documentTemplate.update": 		["POST","/docs/document-template/update/{documentTemplate}"],
    "docs.documentTemplate.delete": 		["GET","/docs/document-template/delete/{documentTemplate}"],
    "docs.documentTemplate.enable": 		["GET","/docs/document-template/enable/{documentTemplate}"],
    "docs.documentTemplate.disable": 		["GET","/docs/document-template/disable/{documentTemplate}"],
    "docs.documentTemplate.getFilters": 		["GET","/docs/document-template/filters"],
    "docs.documentTemplate.setFilters": 		["PUT","/docs/document-template/filters"],
    "docs.documentTemplate.cleanFilters": 		["DELETE","/docs/document-template/filters"],
    "docs.documentTemplate.refresh": 		["GET","/docs/document-template/refresh"],
    "docs.document.generateDocument": 		["POST","/docs/document/generateDocument"],
    "docs.document.generateAllLoanDocuments": 		["POST","/docs/document/generateAllLoanDocuments"],
    "docs.document.getActualByLoan": 		["GET","/docs/document/get-actual-by-loan/{loan}"],
    "docs.documentDownloadLog.create": 		["GET","/docs/document-download/{id}"],
    "head.clientCard.uploadDocument": 		["POST","/docs/clientCard/upload-document"],
    "docs.documentTemplate.copy": 		["GET","/docs/document-template/copy/{documentTemplate}"],
    "head.dashboard": 		["GET","/head/dashboard"],
    "head.dashboard.showData": 		["GET","/head/dashboard/show"],
    "file.download": 		["GET","/head/file/{file}"],
    "file.view": 		["GET","/head/file-view/{file}"],
    "head.creditLimitRules.list": 		["GET","/head/credit-limit-rules"],
    "head.creditLimitRules.create": 		["GET","/head/credit-limit-rules/create"],
    "head.creditLimitRules.store": 		["POST","/head/credit-limit-rules/store"],
    "head.creditLimitRules.edit": 		["GET","/head/credit-limit-rules/edit/{creditLimitRule}"],
    "head.creditLimitRules.update": 		["POST","/head/credit-limit-rules/update/{creditLimitRule}"],
    "head.creditLimitRules.getFilters": 		["GET","/head/credit-limit-rules/filters"],
    "head.creditLimitRules.setFilters": 		["PUT","/head/credit-limit-rules/filters"],
    "head.creditLimitRules.cleanFilters": 		["DELETE","/head/credit-limit-rules/filters"],
    "head.creditLimitRules.refresh": 		["GET","/head/credit-limit-rules/refresh"],
    "head.autoProcessRules.list": 		["GET","/head/auto-process-rule"],
    "head.autoProcessRules.create": 		["GET","/head/auto-process-rule/create"],
    "head.autoProcessRules.store": 		["POST","/head/auto-process-rule/store"],
    "head.autoProcessRules.edit": 		["GET","/head/auto-process-rule/edit/{autoProcessRule}"],
    "head.autoProcessRules.update": 		["POST","/head/auto-process-rule/update/{autoProcessRule}"],
    "head.autoProcessRules.getFilters": 		["GET","/head/auto-process-rule/filters"],
    "head.autoProcessRules.setFilters": 		["PUT","/head/auto-process-rule/filters"],
    "head.autoProcessRules.cleanFilters": 		["DELETE","/head/auto-process-rule/filters"],
    "head.autoProcessRules.refresh": 		["GET","/head/auto-process-rule/refresh"],
    "head.ccrReports.list": 		["GET","/head/ccr-reports"],
    "head.ccrReports.refresh": 		["GET","/head/ccr-reports/refresh"],
    "head.ccrReports.generate": 		["GET","/head/ccr-reports/generate/{type}"],
    "head.ccrReports.download": 		["GET","/head/ccr-reports/download/{fileId}"],
    "head.ccrReports.stats": 		["GET","/head/ccr-stats"],
    "head.ccrReports.stats_refresh": 		["GET","/head/ccr-stats/refresh"],
    "head.client-card-task.index": 		["GET","/head/clientCard/{clientId}/task/{task}/{taskId?}"],
    "head.loan-task.index": 		["GET","/head/clientCard/{clientId}/{loanId}/{task}/{taskId?}"],
    "head.client-with-loan.index": 		["GET","/head/clientCard/{clientId}/{loanId}"],
    "head.client-without-loan.index": 		["GET","/head/clientCard/{clientId}"],
    "head.loan-history.index": 		["GET","/head/ccr-report-history"],
    "head.loan-history.create": 		["POST","/head/ccr-report-history/create-new-ccr-report"],
    "head.noi-reports.index": 		["GET","/head/noi-reports"],
    "head.noi-reports.create": 		["POST","/head/noi-reports/create-new-noi-report"],
    "head.mvr-reports.index": 		["GET","/head/mvr-reports"],
    "head.mvr-reports.create": 		["POST","/head/mvr-reports/create-new-mvr-report"],
    "head.a4e-reports.index": 		["GET","/head/a4e-reports"],
    "head.a4e-reports.create": 		["POST","/head/a4e-reports"],
    "head.client-communication.index": 		["GET","/head/client-communication"],
    "head.client-payments.index": 		["GET","/head/client-payments"],
    "head.client-payment-schedule.index": 		["GET","/head/client-payment-schedule"],
    "head.system-log.index": 		["GET","/head/system-log"],
    "head.prev-requests.index": 		["GET","/head/prev-requests"],
    "head.approveLoans.list": 		["GET","/head/loans-approve"],
    "head.approveLoans.refreshApproved": 		["GET","/head/loans/refresh-approved"],
    "head.approveLoans.processLoanApprove": 		["GET","/head/loans-approve/process-loan-approve/{loan}"],
    "head.loans.sign": 		["GET","/head/loans/sign/{loan}"],
    "head.loans.activate": 		["GET","/head/loans/activate/{loan}"],
    "head.loans.refresh": 		["GET","/head/loans/refresh"],
    "head.loans.export": 		["GET","/head/loans/export"],
    "head.loans.list": 		["GET","/head/loans"],
    "head.loans.historyPaginate": 		["GET","/head/loans/history/{id}"],
    "head.loans.create": 		["GET","/head/loans/create/{pin?}"],
    "head.loans.store": 		["POST","/head/loans/store"],
    "head.loan.client-office-check-products": 		["GET","/head/loan/check-office-products"],
    "head.loan.get-product-settings": 		["GET","/head/loan/get-product-settings"],
    "head.loans.edit": 		["GET","/head/loans/edit/{loan}"],
    "head.loans.update": 		["POST","/head/loans/update/{loan}"],
    "head.loan.client-card-update-loan": 		["POST","/head/loans/update-client-from-client-card"],
    "head.loan.update-payment-method": 		["POST","/head/loans/update-payment-method"],
    "head.loans.getFilters": 		["GET","/head/loans/filters"],
    "head.loans.setFilters": 		["PUT","/head/loans/filters"],
    "head.loans.cleanFilters": 		["DELETE","/head/loans/filters"],
    "head.loans.validatePreliminaryPaymentPlan": 		["GET","/head/loans/validate-preliminary-payment-plan"],
    "head.loans.showPreliminaryPaymentPlan": 		["GET","/head/loans/payment-schedule-plan-preview"],
    "head.loans.earlyRepayment": 		["POST","/head/loans/early-repayment"],
    "head.loans.sendEarlyRepaymentSms": 		["POST","/head/loans/send-early-repayment-sms"],
    "head.loans.sendEarlyRepaymentEmail": 		["POST","/head/loans/send-early-repayment-email"],
    "head.loans.earlyRepaymentApprove": 		["POST","/head/loans/early-repayment-approve"],
    "head.loan.update-loan-consultant": 		["POST","/head/loan/update-loan-consultant"],
    "head.banks.list": 		["GET","/head/banks"],
    "head.banks.getBankById": 		["GET","/head/banks/getBankById"],
    "head.banks.getBankByIban": 		["GET","/head/banks/getBankByIban"],
    "head.bankAccount.list": 		["GET","/head/bank-accounts"],
    "head.bankAccount.create": 		["GET","/head/bank-account/create"],
    "head.bankAccount.store": 		["POST","/head/bank-account/store"],
    "head.bankAccount.edit": 		["GET","/head/bank-account/edit/{bankAccount}"],
    "head.bankAccount.update": 		["POST","/head/bank-account/update/{bankAccount}"],
    "head.bankAccount.delete": 		["GET","/head/bank-account/delete/{bankAccount}"],
    "head.bankAccount.enable": 		["GET","/head/bank-account/enable/{bankAccount}"],
    "head.bankAccount.disable": 		["GET","/head/bank-account/disable/{bankAccount}"],
    "head.bankAccount.getFilters": 		["GET","/head/bank-account/filters"],
    "head.bankAccount.setFilters": 		["PUT","/head/bank-account/filters"],
    "head.bankAccount.cleanFilters": 		["DELETE","/head/bank-account/filters"],
    "head.bankAccount.refresh": 		["GET","/head/bank-account/refresh"],
    "head.blockReason.list": 		["GET","/head/block-reasons"],
    "head.blockReason.create": 		["GET","/head/block-reasons/create"],
    "head.blockReason.store": 		["POST","/head/block-reasons/store"],
    "head.blockReason.edit": 		["GET","/head/block-reasons/edit/{blockReason}"],
    "head.blockReason.update": 		["POST","/head/block-reasons/update/{blockReason}"],
    "head.blockReason.delete": 		["GET","/head/block-reasons/delete/{blockReason}"],
    "head.blockReason.enable": 		["GET","/head/block-reasons/enable/{blockReason}"],
    "head.blockReason.disable": 		["GET","/head/block-reasons/disable/{blockReason}"],
    "head.deleteReason.list": 		["GET","/head/delete-reasons"],
    "head.deleteReason.create": 		["GET","/head/delete-reasons/create"],
    "head.deleteReason.store": 		["POST","/head/delete-reasons/store"],
    "head.deleteReason.edit": 		["GET","/head/delete-reasons/edit/{deleteReason}"],
    "head.deleteReason.update": 		["POST","/head/delete-reasons/update/{deleteReason}"],
    "head.deleteReason.delete": 		["GET","/head/delete-reasons/delete/{deleteReason}"],
    "head.deleteReason.enable": 		["GET","/head/delete-reasons/enable/{deleteReason}"],
    "head.deleteReason.disable": 		["GET","/head/delete-reasons/disable/{deleteReason}"],
    "head.employments.list": 		["GET","/head/employments"],
    "head.employments.create": 		["GET","/head/employments/create"],
    "head.employments.store": 		["POST","/head/employments/store"],
    "head.employments.edit": 		["GET","/head/employments/edit/{id}"],
    "head.employments.update": 		["POST","/head/employments/update/{id}"],
    "head.employments.delete": 		["GET","/head/employments/delete/{id}"],
    "head.employments.enable": 		["GET","/head/employments/enable/{id}"],
    "head.employments.disable": 		["GET","/head/employments/disable/{id}"],
    "head.clients.refresh": 		["GET","/head/clients/refresh"],
    "head.clients.export": 		["GET","/head/clients/export"],
    "head.clients.list": 		["GET","/head/clients"],
    "head.clients.historyPaginate": 		["GET","/head/clients/history/{id}"],
    "head.clients.create": 		["GET","/head/clients/create"],
    "head.clients.store": 		["POST","/head/clients/store"],
    "head.clients.edit": 		["GET","/head/clients/edit/{client}"],
    "head.clients.updateClientData": 		["POST","/head/clients/update/{client}"],
    "head.clients.delete": 		["GET","/head/clients/delete/{client}"],
    "head.clients.block": 		["GET","/head/clients/block/{client}"],
    "head.clients.unblock": 		["GET","/head/clients/unblock/{client}"],
    "head.clients.enable": 		["GET","/head/clients/enable/{client}"],
    "head.clients.disable": 		["GET","/head/clients/disable/{client}"],
    "head.clients.search": 		["GET","/head/clients/search"],
    "head.clients.addMultipleContacts": 		["GET","/head/clients/multiple-contact"],
    "head.client-guarantors.store": 		["POST","/head/client-guarantors"],
    "head.client-guarantors.destroy": 		["DELETE","/head/client-guarantors/{guarantor}"],
    "head.clients.addMultipleGuarants": 		["GET","/head/clients/multiple-guarants"],
    "head.clients.deleteGuarant": 		["DELETE","/head/clients/delete-guarant/{client}/{seqNum}/{guarant}"],
    "head.client-contacts.create": 		["POST","/head/client-contacts/create"],
    "head.client-contacts.edit": 		["PUT","/head/client-contacts/{contact}"],
    "head.client-contacts.destroy": 		["DELETE","/head/client-contacts/{loan_contact_actual}"],
    "head.clients.deleteContact": 		["DELETE","/head/clients/delete-contact/{client}/{seqNum}/{contact}"],
    "head.clients.guarantPrefill": 		["GET","/head/guarant-prefill"],
    "head.clients.getFilters": 		["GET","/head/clients/filters"],
    "head.clients.setFilters": 		["PUT","/head/clients/filters"],
    "head.clients.cleanFilters": 		["DELETE","/head/clients/filters"],
    "head.clientCard.completeSaleTask": 		["POST","/head/clientCard/complete-sale-task/{saleTask}"],
    "head.clients.cardProfile": 		["GET","/head/clientCard/{clientId}/{second?}/{third?}/{fourth?}"],
    "head.clientCard.layout": 		["POST","/head/clientCard/layout"],
    "head.clientCard.loadLayout": 		["POST","/head/clientCard/loadLayout"],
    "head.clientCard.blockClient": 		["POST","/head/clientCard/block-client/{client}"],
    "head.clientCard.unblockClient": 		["POST","/head/clientCard/unblock-client/{client}"],
    "head.clientCard.checkIfLoanIsProcessed": 		["GET","/head/clientCard/check-if-loan-is-processed"],
    "head.clientCard.acquireMoney": 		["POST","/head/clientCard/acquire-money/{loan}"],
    "head.clientCard.cancelLoan": 		["POST","/head/clientCard/refuse-money"],
    "head.clientCard.recalculateCurrentLoan": 		["POST","/head/clientCard/recalculateCurrentLoan"],
    "head.clientCard.export-previous-requests-to-pdf": 		["GET","/head/clientCard/export-previous-requests-pdf/{loan}"],
    "head.clientCard.refresh-previous-requests": 		["GET","/head/clientCard/refresh-previous-requests/{client}"],
    "head.clientCard.refresh-system-logs": 		["GET","/head/clientCard/refresh-system-logs/{client}"],
    "head.clientCard.refresh-open-tasks": 		["GET","/head/clientCard/refresh-open-tasks/{client}"],
    "head.client-phone.storePhone": 		["POST","/head/client-phone"],
    "head.client-phone.destroy": 		["GET","/head/client-phone/{client_phone}"],
    "head.client-phone.edit": 		["GET","/head/client-phone/{client_phone}/edit"],
    "head.client-phone.update": 		["PUT","/head/client-phone/{client_phone}"],
    "head.client.updatePhone": 		["POST","/head/clientCard/update-phone"],
    "head.client.addPhone": 		["POST","/head/clientCard/add-phone"],
    "head.client.deletePhone": 		["DELETE","/head/clientCard/client-phone/{clientPhone}"],
    "head.client.updateAddress": 		["POST","/head/clientCard/update-address"],
    "head.client-address.create": 		["GET","/head/client-address/create"],
    "head.client-address.store": 		["POST","/head/client-address"],
    "head.client-address.delete": 		["DELETE","/head/client-address/{clientAddress}"],
    "head.client.updateGuarant": 		["POST","/head/clientCard/update-guarant"],
    "head.client.createGuarant": 		["POST","/head/clientCard/create-guarant"],
    "head.client.updateContact": 		["POST","/head/clientCard/update-contact"],
    "head.client.createContact": 		["POST","/head/clientCard/create-contact"],
    "head.clientCard.approveAttempt": 		["POST","/head/clientCard/processing-approve"],
    "head.clientCard.update-employer": 		["POST","/head/clientCard/update-employer"],
    "head.clientCard.refreshPayments": 		["GET","/head/clientCard/refresh-payments"],
    "head.clientCard.prefillConsultantsOffice": 		["GET","/head/clientCard/prefill-offices-consultants"],
    "head.clientCard.prefillOffices": 		["GET","/head/clientCard/prefill-offices"],
    "head.clientCard.updateOfficeConsultant": 		["POST","/head/clientCard/updateOfficeConsultant"],
    "head.clientCard.refreshInstallments": 		["GET","/head/clientCard/installments/refresh"],
    "head.clientCard.refreshTaxes": 		["GET","/head/clientCard/taxes/refresh"],
    "head.clientCard.createNewCcrReport": 		["POST","/head/clientCard/create-new-ccr-report"],
    "head.clientCard.createNewNoiReport": 		["POST","/head/clientCard/create-new-noi-report"],
    "head.clientCard.createNewMvrReport": 		["POST","/head/clientCard/create-new-mvr-report"],
    "head.clientCard.createNewA4EReport": 		["POST","/head/clientCard/create-new-a4e-report"],
    "head.clientCard.printDocuments": 		["GET","/head/clientCard/print-documents/{printFlag}/{loan}"],
    "head.clientCard.loans": 		["GET","/head/clientCard/refresh-client-loans/{client}"],
    "head.clientCard.loadTab": 		["GET","/head/clientCard/load-tab"],
    "head.clientCard.refreshClientCommunication": 		["GET","/head/clientCard/refresh-client-communication/{client}"],
    "head.clientCard.renderTab": 		["POST","/head/clientCard/render-tab"],
    "head.clientCard.getClientLoansRest": 		["GET","/head/clientCard/get-client-loans-rest"],
    "head.clientCard.getAllGuarantTypesRest": 		["GET","/head/clientCard/get-all-guarant-types-rest"],
    "head.clientCard.getAllContactTypesRest": 		["GET","/head/clientCard/get-all-contact-types-rest"],
    "head.clientCard.getClientLoanStatisticsRest": 		["GET","/head/clientCard/get-client-loan-statistics-rest"],
    "head.clientCard.getCitiesRest": 		["GET","/head/clientCard/get-cities-rest"],
    "head.clientCard.getAllOfficesRest": 		["GET","/head/clientCard/get-all-offices-rest"],
    "head.clientCard.getAllPaymentMethodsRest": 		["GET","/head/clientCard/get-all-payment-methods-rest"],
    "head.clientCard.getManualTemplatesRest": 		["GET","/head/clientCard/get-manual-templates-rest"],
    "head.clientCard.getSaleDecisionsRest": 		["GET","/head/clientCard/get-sale-decisions-rest"],
    "head.clientCard.getRefinanceParamsRest": 		["GET","/head/clientCard/get-refinance-params-rest"],
    "head.clientCard.getCalculatorDataByLoanRest": 		["GET","/head/clientCard/get-calculator-data-by-loan-rest"],
    "head.clientCard.getAllClientOpenTasksRest": 		["GET","/head/clientCard/get-all-client-open-tasks-rest"],
    "head.clientCard.getClientLoansLightRest": 		["GET","/head/clientCard/get-client-loans-light-rest"],
    "head.clientCard.getClientSmsRest": 		["GET","/head/clientCard/get-client-sms-rest"],
    "head.clientCard.getAllCcrReportsByPinRest": 		["GET","/head/clientCard/get-all-ccr-reports-by-pin-rest"],
    "head.clientCard.hasCcrReportsPermissionRest": 		["GET","/head/clientCard/has-ccr-reports-permission-rest"],
    "head.clientCard.getNoiReportsRest": 		["GET","/head/clientCard/get-noi-reports-rest"],
    "head.clientCard.hasNoiReportsPermissionRest": 		["GET","/head/clientCard/has-noi-reports-permission-rest"],
    "head.clientCard.getMvrReportsRest": 		["GET","/head/clientCard/get-mvr-reports-rest"],
    "head.clientCard.hasMvrReportsPermissionRest": 		["GET","/head/clientCard/has-mvr-reports-permission-rest"],
    "head.clientCard.getA4eReportsRest": 		["GET","/head/clientCard/get-a4e-reports-rest"],
    "head.clientCard.hasA4eReportsPermissionRest": 		["GET","/head/clientCard/has-a4e-reports-permission-rest"],
    "head.clientCard.getClientCommunicationRest": 		["GET","/head/clientCard/get-client-communication-rest"],
    "head.clientCard.getAllPaymentSourcesRest": 		["GET","/head/clientCard/get-all-payment-sources-rest"],
    "head.clientCard.getPaymentTableDataRest": 		["GET","/head/clientCard/get-payment-table-data-rest"],
    "head.clientCard.getLoanExtensionDataRest": 		["GET","/head/clientCard/get-loan-extension-data-rest"],
    "head.clientCard.getLoanTaxesRest": 		["GET","/head/clientCard/get-loan-taxes-rest"],
    "head.clientCard.calculateExtensionFeeRest": 		["GET","/head/clientCard/calculate-extension-fee-rest"],
    "head.clientCard.getLoanCommonInformationRest": 		["GET","/head/clientCard/get-loan-common-information-rest"],
    "head.clientCard.getLoanDuesRest": 		["GET","/head/clientCard/get-loan-dues-rest"],
    "head.clientCard.getLoanPaidUntilNowRest": 		["GET","/head/clientCard/get-loan-paid-until-now-rest"],
    "head.clientCard.getLoanLeftToPayRest": 		["GET","/head/clientCard/get-loan-left-to-pay-rest"],
    "head.clientCard.getLoanMaturedAmountsRest": 		["GET","/head/clientCard/get-loan-matured-amounts-rest"],
    "head.clientCard.getLoanOverviewRest": 		["GET","/head/clientCard/get-loan-overview-rest"],
    "head.clientCard.getSystemLogsTableDataRest": 		["GET","/head/clientCard/get-system-logs-table-data-rest"],
    "head.clients.sessions": 		["GET","/head/clients/sessions"],
    "head.clients.sessionDelete": 		["POST","/head/clients/sessions/delete/{id}"],
    "head.interestTerm.list": 		["GET","/head/interest-term"],
    "head.interestTerm.detail": 		["GET","/head/interest-term/detail/{productId}"],
    "head.interestTerm.import": 		["POST","/head/interest-term/import-interest-term"],
    "head.interestTerm.refresh": 		["GET","/head/interest-term/refresh"],
    "head.penaltyTerm.list": 		["GET","/head/penalty-term"],
    "head.penaltyTerm.detail": 		["GET","/head/penalty-term/detail/{productId}"],
    "head.penaltyTerm.import": 		["POST","/head/penalty-term/import-penalty-term"],
    "head.penaltyTerm.refresh": 		["GET","/head/penalty-term/refresh"],
    "payment.payments.list": 		["GET","/payments/all-payments"],
    "payment.payments.refresh": 		["GET","/payments/all-payments/refresh"],
    "payment.refundPage": 		["GET","/payments/all-payments/refund-page/{payment?}"],
    "payment.refund": 		["POST","/payments/all-payments/refund/{payment?}"],
    "payment.refundState": 		["POST","/payments/all-payments/refund-state/{payment?}"],
    "payment.manual-payment.client-overview": 		["GET","/payments/manual-payment/overview/{client?}"],
    "payment.manual-payment.payment-overview": 		["GET","/payments/manual-payment/payment/{payment?}"],
    "payment.payment.rollback": 		["POST","/payments/payments/{payment?}"],
    "payment.payment-schedule.get": 		["GET","/payments/payment-schedule/{loan}/{paymentTask?}"],
    "payments.client.search": 		["GET","/payments/search-client-by-key"],
    "payments.client.get-client-data": 		["GET","/payments/getClientData/{client}/{payment?}"],
    "payments.spread-payment.post": 		["POST","/payments/spreadPayment/{loan}/{payment?}"],
    "payments.save-payment.post": 		["POST","/payments/savePayment"],
    "payments.edit-payment.post": 		["POST","/payments/savePayment/{payment}"],
    "payments.easypay.listRequest": 		["GET","/payments/easypa-requests/log"],
    "payments.easypay.getRequestFilters": 		["GET","/payments/easypa-requests/log/filters"],
    "payments.easypay.setRequestFilters": 		["PUT","/payments/easypa-requests/log/filters"],
    "payments.easypay.cleanRequestFilters": 		["DELETE","/payments/easypa-requests/log/filterss"],
    "payments.easypay.refreshRequest": 		["GET","/payments/easypa-requests/log/refresh"],
    "payment.paymentsTasks.list": 		["GET","/payments/payment-tasks"],
    "payment.paymentsTasks.refresh": 		["GET","/payments/payment-tasks/refresh"],
    "payment.paymentsTasks.export": 		["GET","/payments/export"],
    "payment.paymentsTasks.processing": 		["GET","/payments/payment-tasks-processing/{paymentTask}"],
    "payment.paymentsTasks.exit-task": 		["GET","/payments/payment-tasks/{paymentTask}/exit"],
    "payment.paymentsTasks.import": 		["POST","/payments/payment-tasks/import"],
    "payment.paymentsTasks.cancel-task": 		["POST","/payments/payment-tasks/cancel/{paymentTask}"],
    "payment.paymentsTasks.cancel-task-and-loan": 		["POST","/payments/payment-tasks/cancelWithLoan/{paymentTask}"],
    "payment.paymentsTasks.approve-outgoing-task": 		["POST","/payments/ordering-tasks/approve-outgoing/{paymentTask}/{loan}"],
    "payment.paymentsTasks.reSendEasypayRequest": 		["POST","/payments/ordering-tasks/resend-easypay/{paymentTask}/{loan}"],
    "payment.paymentTasks.completePaymentTask": 		["POST","/payments/complete-payment-task/{paymentTask}"],
    "payments.tax.create": 		["POST","/payments/tax/create"],
    "payments.tax.addAmount": 		["POST","/payments/tax/add-amount"],
    "payments.tax.extendLoan": 		["POST","/payments/tax/extend-loan"],
    "payments.tax.destroy": 		["DELETE","/payments/tax/{tax}"],
    "payments.tax.updateTaxes": 		["POST","/payments/tax/update-taxes"],
    "payments.tax.calculateLoanExtendFee": 		["GET","/payments/tax/calculate-loan-extend-fee"],
    "payments.paymentTaskDecision.list": 		["GET","/payments/payment-task-decision"],
    "payments.paymentTaskDecision.create": 		["GET","/payments/payment-task-decision/create"],
    "payments.paymentTaskDecision.store": 		["POST","/payments/payment-task-decision/store"],
    "payments.paymentTaskDecision.edit": 		["GET","/payments/payment-task-decision/edit/{paymentTaskDecision}"],
    "payments.paymentTaskDecision.update": 		["POST","/payments/payment-task-decision/update/{paymentTaskDecision}"],
    "payments.paymentTaskDecision.delete": 		["GET","/payments/payment-task-decision/delete/{paymentTaskDecision}"],
    "payments.paymentTaskDecision.enable": 		["GET","/payments/payment-task-decision/enable/{paymentTaskDecision}"],
    "payments.paymentTaskDecision.disable": 		["GET","/payments/payment-task-decision/disable/{paymentTaskDecision}"],
    "payment.manual-payment.index": 		["GET","/payments/manual-payment"],
    "payment.manual-payment.overview": 		["GET","/payments/manual-payment/{paymentTask}/overview"],
    "payment.manual-payment.storeManualPayment": 		["POST","/payments/manual-payment/save-payment"],
    "payment.manual-payment.load-loans": 		["GET","/payments/manual-payment/load-loans"],
    "payment.manual-payment.load-loans-info": 		["GET","/payments/manual-payment/load-loans-info"],
    "sales.saleDecision.list": 		["GET","/sales/sale-decision"],
    "sales.saleDecision.create": 		["GET","/sales/sale-decision/create"],
    "sales.saleDecision.store": 		["POST","/sales/sale-decision/store"],
    "sales.saleDecision.edit": 		["GET","/sales/sale-decision/edit/{saleDecision}"],
    "sales.saleDecision.update": 		["POST","/sales/sale-decision/update/{saleDecision}"],
    "sales.saleDecision.delete": 		["GET","/sales/sale-decision/delete/{saleDecision}"],
    "sales.saleDecision.enable": 		["GET","/sales/sale-decision/enable/{saleDecision}"],
    "sales.saleDecision.disable": 		["GET","/sales/sale-decision/disable/{saleDecision}"],
    "sales.saleDecisionReason.list": 		["GET","/sales/sale-decision-reason"],
    "sales.saleDecisionReason.create": 		["GET","/sales/sale-decision-reason/create"],
    "sales.saleDecisionReason.store": 		["POST","/sales/sale-decision-reason/store"],
    "sales.saleDecisionReason.edit": 		["GET","/sales/sale-decision-reason/edit/{saleDecisionReason}"],
    "sales.saleDecisionReason.update": 		["POST","/sales/sale-decision-reason/update/{saleDecisionReason}"],
    "sales.saleDecisionReason.delete": 		["GET","/sales/sale-decision-reason/delete/{saleDecisionReason}"],
    "sales.saleDecisionReason.enable": 		["GET","/sales/sale-decision-reason/enable/{saleDecisionReason}"],
    "sales.saleDecisionReason.disable": 		["GET","/sales/sale-decision-reason/disable/{saleDecisionReason}"],
    "sales.saleTask.list": 		["GET","/sales/sale-task"],
    "sales.saleTask.refresh": 		["GET","/sales/sale-task/refresh"],
    "sales.saleTask.getFilters": 		["GET","/sales/sale-task/filters"],
    "sales.saleTask.setFilters": 		["PUT","/sales/sale-task/filters"],
    "sales.saleTask.cleanFilters": 		["DELETE","/sales/sale-task/filters"],
    "sales.processing": 		["GET","/sales/processing/{saleTask}"],
    "sales.saleTask.ajaxTimer": 		["GET","/sales/timer"],
    "sales.setOfficeId": 		["GET","/sales/new-application/set-office"],
    "sales.newAppFromSaleTask": 		["GET","/sales/new-application/{saleTask}/from-sale-task"],
    "sales.newApplication": 		["GET","/sales/new-application"],
    "sales.saveNewApplication": 		["POST","/sales/new-application"],
    "sales.storeNewCompanyApplication": 		["POST","/sales/new-application/company"],
    "sales.checkClientFromMvr": 		["GET","/sales/check-client-from-mvr"],
    "sales.getProductSettingsByProduct": 		["GET","/sales/product-settings/{product}/{client?}"],
    "sales.getClientLoanData": 		["GET","/sales/get-client-loan-data"],
    "sales.calculateLoanParams": 		["GET","/sales/calc-params"],
    "sales.getActiveLoans": 		["GET","/sales/loans/{client}/{loanId?}"],
    "sales.getProductsByOffice": 		["GET","/sales/get-products-by-office/{office}"],
    "sales.getRestAfterRefinance": 		["POST","/sales/get-rest-after-refinance"],
    "sales.refinance-slider.fetch-refinance-loans-info": 		["POST","/sales/fetch-refinance-loans-info"]
}

/**
 * @typedef {('home'|'set-pagination-limit'|'admin.administrators.list'|'admin.administrators.getFilters'|'admin.administrators.setFilters'|'admin.administrators.cleanFilters'|'admin.administrators.refresh'|'admin.administrators.create'|'admin.administrators.store'|'admin.administrators.edit'|'admin.administrators.update'|'admin.administrators.delete'|'admin.administrators.enable'|'admin.administrators.disable'|'admin.agreements.refresh'|'admin.agreements.getFilters'|'admin.agreements.setFilters'|'admin.agreements.cleanFilters'|'admin.agreements.list'|'admin.agreements.create'|'admin.agreements.store'|'admin.agreements.edit'|'admin.agreements.update'|'admin.agreements.delete'|'admin.agreements.enable'|'admin.agreements.disable'|'admin.branches.list'|'admin.branches.create'|'admin.branches.store'|'admin.branches.edit'|'admin.branches.update'|'admin.branches.delete'|'admin.branches.enable'|'admin.branches.disable'|'admin.branches.refresh'|'admin.branches.getFilters'|'admin.branches.setFilters'|'admin.branches.cleanFilters'|'admin.close-reasons.refresh'|'admin.close-reasons.getFilters'|'admin.close-reasons.setFilters'|'admin.close-reasons.cleanFilters'|'admin.close-reasons.list'|'admin.close-reasons.create'|'admin.close-reasons.store'|'admin.close-reasons.edit'|'admin.close-reasons.update'|'admin.close-reasons.delete'|'admin.close-reasons.enable'|'admin.close-reasons.disable'|'admin.landing.sections'|'admin.landing.sectionsCreate'|'admin.landing.sectionsEdit'|'admin.landing.sectionsCreateSubmit'|'admin.landing.sectionsDisableEnable'|'admin.landing.sectionsPriorityUpDown'|'admin.landing.docs'|'admin.landing.docsCreate'|'admin.landing.docEdit'|'admin.landing.docsCreateSubmit'|'admin.landing.docDisableEnable'|'admin.landing.docsList'|'admin.offices.fetchOfficeAdministrators'|'admin.offices.getFilters'|'admin.offices.setFilters'|'admin.offices.cleanFilters'|'admin.offices.refresh'|'admin.offices.list'|'admin.offices.create'|'admin.offices.store'|'admin.offices.edit'|'admin.offices.update'|'admin.offices.delete'|'admin.offices.enable'|'admin.offices.disable'|'admin.roles.getFilters'|'admin.roles.setFilters'|'admin.roles.cleanFilters'|'admin.roles.refresh'|'admin.roles.list'|'admin.roles.create'|'admin.roles.store'|'admin.roles.edit'|'admin.roles.update'|'admin.roles.delete'|'admin.roles.enable'|'admin.roles.disable'|'admin.setting.types.list'|'admin.setting.types.create'|'admin.setting.types.store'|'admin.setting.types.edit'|'admin.setting.types.update'|'admin.setting.types.delete'|'admin.setting.types.enable'|'admin.setting.types.disable'|'admin.settings.list'|'admin.settings.create'|'admin.settings.store'|'admin.settings.edit'|'admin.settings.update'|'admin.settings.delete'|'admin.settings.enable'|'admin.settings.disable'|'admin.settings.getFilters'|'admin.settings.setFilters'|'admin.settings.cleanFilters'|'admin.settings.refresh'|'admin.tmp-request-steps.refresh'|'admin.tmp-request-steps.getFilters'|'admin.tmp-request-steps.setFilters'|'admin.tmp-request-steps.cleanFilters'|'admin.tmp-request-steps.list'|'admin.tmp-request-steps.create'|'admin.tmp-request-steps.store'|'admin.tmp-request-steps.edit'|'admin.tmp-request-steps.update'|'admin.tmp-request-steps.delete'|'admin.tmp-request-steps.enable'|'admin.tmp-request-steps.disable'|'admin.fiscal-devices.create'|'admin.fiscal-devices.edit'|'admin.fiscal-devices.delete'|'approve.loan-decision.approve'|'approve.loan-decision.cancel'|'approve.loan-decision.delay'|'approve.loan-decision.process'|'approve.approveDecision.list'|'approve.approveDecision.create'|'approve.approveDecision.store'|'approve.approveDecision.edit'|'approve.approveDecision.update'|'approve.approveDecision.delete'|'approve.approveDecision.enable'|'approve.approveDecision.disable'|'approve.approveDecisionReason.list'|'approve.approveDecisionReason.create'|'approve.approveDecisionReason.store'|'approve.approveDecisionReason.edit'|'approve.approveDecisionReason.update'|'approve.approveDecisionReason.delete'|'approve.approveDecisionReason.enable'|'approve.approveDecisionReason.disable'|'payment.cashDesk.selectOffice'|'payment.cashDesk.topPanel'|'payment.cashDesk.list'|'payment.cashDesk.refresh'|'payment.cashDesk.tryAgain'|'payment.cashDesk.clearTremolSession'|'payment.cashDesk.getModal'|'payment.cashDesk.getInitBalanceModal'|'payment.cashDesk.edit'|'payment.cashDesk.add'|'payment.cashDesk.initBalance'|'payment.cashDesk.update'|'payment.cashDesk.export'|'payment.cashDesk.download'|'payment.cashDesk.getDocument'|'payment.cashDesk.taxFeeTransaction'|'terminal-log.index'|'terminal-log.show'|'terminal-log.confirmation'|'terminal-log.resend'|'terminal-log.test'|'collect.collector-decisions.list'|'collect.collector-decisions.create'|'collect.collector-decisions.store'|'collect.collector-decisions.edit'|'collect.collector-decisions.update'|'collect.collector-decisions.delete'|'collect.collector-decisions.enable'|'collect.collector-decisions.disable'|'collect.collector-decisions.refresh'|'collect.collector-decisions.getFilters'|'collect.collector-decisions.setFilters'|'collect.collector-decisions.cleanFilters'|'collect.buckets.list'|'collect.buckets.create'|'collect.buckets.store'|'collect.buckets.edit'|'collect.buckets.update'|'collect.buckets.delete'|'collect.buckets.enable'|'collect.buckets.disable'|'collect.buckets.refresh'|'collect.buckets.getFilters'|'collect.buckets.setFilters'|'collect.buckets.cleanFilters'|'collect.bucket-tasks.list'|'collect.bucket-tasks.refresh'|'collect.bucket-tasks.getFilters'|'collect.bucket-tasks.setFilters'|'collect.bucket-tasks.cleanFilters'|'collect.bucket-tasks.process'|'collect.collector-attempt.create'|'collect.mass-collector-attempt.create'|'collect.legal-docs.choose'|'collect.legal-docs.filter'|'collect.loan-buckets.list'|'collect.loan-buckets.store'|'common.payment-methods.options-select'|'common.slider.fetch-product-settings'|'common.slider.calculateLoan'|'common.loans.cities'|'common.loans.city'|'common.loans.getCityName'|'common.client-card-boxes.index'|'common.client-card-boxes.edit'|'common.client-card-boxes.update-box'|'common.client-card-boxes.save-box-order'|'communication.clientCardCommunication.sendEarlyCommunication'|'communication.email.list'|'communication.email.preview'|'communication.email.sendEmail'|'communication.emailTemplate.list'|'communication.emailTemplate.create'|'communication.emailTemplate.store'|'communication.emailTemplate.edit'|'communication.emailTemplate.update'|'communication.emailTemplate.delete'|'communication.emailTemplate.enable'|'communication.emailTemplate.disable'|'communication.emailTemplate.revert'|'communication.emailTemplate.getFilters'|'communication.emailTemplate.setFilters'|'communication.emailTemplate.cleanFilters'|'communication.emailTemplate.refresh'|'communication.sms.list'|'communication.sms.sendSms'|'communication.smsTemplate.list'|'communication.smsTemplate.create'|'communication.smsTemplate.store'|'communication.smsTemplate.edit'|'communication.smsTemplate.update'|'communication.smsTemplate.delete'|'communication.smsTemplate.enable'|'communication.smsTemplate.disable'|'communication.smsTemplate.revert'|'communication.smsTemplate.getFilters'|'communication.smsTemplate.setFilters'|'communication.smsTemplate.cleanFilters'|'communication.smsTemplate.refresh'|'communication.viber.list'|'communication.viber.send'|'communication.viber.getFilters'|'communication.viber.setFilters'|'communication.viber.cleanFilters'|'communication.viber.refresh'|'communication.viberTemplate.list'|'communication.viberTemplate.create'|'communication.viberTemplate.store'|'communication.viberTemplate.edit'|'communication.viberTemplate.update'|'communication.viberTemplate.delete'|'communication.viberTemplate.enable'|'communication.viberTemplate.disable'|'communication.viberTemplate.revert'|'communication.viberTemplate.getFilters'|'communication.viberTemplate.setFilters'|'communication.viberTemplate.cleanFilters'|'communication.viberTemplate.refresh'|'communication.notificationSetting.edit'|'communication.notificationSetting.communicationTabEdit'|'communication.notification-setting.updateClientNotificationSettings'|'communication.communicationComment.createCommunicationComment'|'communication.template.preview'|'communication.template.download'|'communication.template.send'|'head.discountsClients.list'|'head.clients.removeDiscount'|'head.clients.massRemoveDiscounts'|'head.clients.refreshDiscounts'|'head.clients.setDiscountsFilters'|'head.clients.getDiscountsFilters'|'head.clients.cleanDiscountsFilters'|'head.clients.importClientsDiscount'|'head.clients.importClientsDiscountManual'|'head.discountsClients.search'|'docs.documentTemplate.list'|'docs.documentTemplate.create'|'docs.documentTemplate.store'|'docs.documentTemplate.edit'|'docs.documentTemplate.update'|'docs.documentTemplate.delete'|'docs.documentTemplate.enable'|'docs.documentTemplate.disable'|'docs.documentTemplate.getFilters'|'docs.documentTemplate.setFilters'|'docs.documentTemplate.cleanFilters'|'docs.documentTemplate.refresh'|'docs.document.generateDocument'|'docs.document.generateAllLoanDocuments'|'docs.document.getActualByLoan'|'docs.documentDownloadLog.create'|'head.clientCard.uploadDocument'|'docs.documentTemplate.copy'|'head.dashboard'|'head.dashboard.showData'|'file.download'|'file.view'|'head.creditLimitRules.list'|'head.creditLimitRules.create'|'head.creditLimitRules.store'|'head.creditLimitRules.edit'|'head.creditLimitRules.update'|'head.creditLimitRules.getFilters'|'head.creditLimitRules.setFilters'|'head.creditLimitRules.cleanFilters'|'head.creditLimitRules.refresh'|'head.autoProcessRules.list'|'head.autoProcessRules.create'|'head.autoProcessRules.store'|'head.autoProcessRules.edit'|'head.autoProcessRules.update'|'head.autoProcessRules.getFilters'|'head.autoProcessRules.setFilters'|'head.autoProcessRules.cleanFilters'|'head.autoProcessRules.refresh'|'head.ccrReports.list'|'head.ccrReports.refresh'|'head.ccrReports.generate'|'head.ccrReports.download'|'head.ccrReports.stats'|'head.ccrReports.stats_refresh'|'head.client-card-task.index'|'head.loan-task.index'|'head.client-with-loan.index'|'head.client-without-loan.index'|'head.ccr-report-history.index'|'head.ccr-report-history.create'|'head.noi-reports.index'|'head.noi-reports.create'|'head.mvr-reports.index'|'head.mvr-reports.create'|'head.a4e-reports.index'|'head.a4e-reports.create'|'head.client-communication.index'|'head.client-payments.index'|'head.client-payment-schedule.index'|'head.system-log.index'|'head.prev-requests.index'|'head.approveLoans.list'|'head.approveLoans.refreshApproved'|'head.approveLoans.processLoanApprove'|'head.loans.sign'|'head.loans.activate'|'head.loans.refresh'|'head.loans.export'|'head.loans.list'|'head.loans.historyPaginate'|'head.loans.create'|'head.loans.store'|'head.loan.client-office-check-products'|'head.loan.get-product-settings'|'head.loans.edit'|'head.loans.update'|'head.loan.client-card-update-loan'|'head.loan.update-payment-method'|'head.loans.getFilters'|'head.loans.setFilters'|'head.loans.cleanFilters'|'head.loans.validatePreliminaryPaymentPlan'|'head.loans.showPreliminaryPaymentPlan'|'head.loans.earlyRepayment'|'head.loans.sendEarlyRepaymentSms'|'head.loans.sendEarlyRepaymentEmail'|'head.loans.earlyRepaymentApprove'|'head.loan.update-loan-consultant'|'head.banks.list'|'head.banks.getBankById'|'head.banks.getBankByIban'|'head.bankAccount.list'|'head.bankAccount.create'|'head.bankAccount.store'|'head.bankAccount.edit'|'head.bankAccount.update'|'head.bankAccount.delete'|'head.bankAccount.enable'|'head.bankAccount.disable'|'head.bankAccount.getFilters'|'head.bankAccount.setFilters'|'head.bankAccount.cleanFilters'|'head.bankAccount.refresh'|'head.blockReason.list'|'head.blockReason.create'|'head.blockReason.store'|'head.blockReason.edit'|'head.blockReason.update'|'head.blockReason.delete'|'head.blockReason.enable'|'head.blockReason.disable'|'head.deleteReason.list'|'head.deleteReason.create'|'head.deleteReason.store'|'head.deleteReason.edit'|'head.deleteReason.update'|'head.deleteReason.delete'|'head.deleteReason.enable'|'head.deleteReason.disable'|'head.employments.list'|'head.employments.create'|'head.employments.store'|'head.employments.edit'|'head.employments.update'|'head.employments.delete'|'head.employments.enable'|'head.employments.disable'|'head.clients.refresh'|'head.clients.export'|'head.clients.list'|'head.clients.historyPaginate'|'head.clients.create'|'head.clients.store'|'head.clients.edit'|'head.clients.update'|'head.clients.delete'|'head.clients.block'|'head.clients.unblock'|'head.clients.enable'|'head.clients.disable'|'head.clients.search'|'head.clients.addMultipleContacts'|'head.client-guarantors.store'|'head.client-guarantors.destroy'|'head.clients.addMultipleGuarants'|'head.clients.deleteGuarant'|'head.client-contacts.create'|'head.client-contacts.edit'|'head.client-contacts.destroy'|'head.clients.deleteContact'|'head.clients.guarantPrefill'|'head.clients.getFilters'|'head.clients.setFilters'|'head.clients.cleanFilters'|'head.clientCard.completeSaleTask'|'head.clients.cardProfile'|'head.clientCard.layout'|'head.clientCard.loadLayout'|'head.clientCard.blockClient'|'head.clientCard.unblockClient'|'head.clientCard.checkIfLoanIsProcessed'|'head.clientCard.acquireMoney'|'head.clientCard.cancelLoan'|'head.clientCard.recalculateCurrentLoan'|'head.clientCard.export-previous-requests-to-pdf'|'head.clientCard.refresh-previous-requests'|'head.clientCard.refresh-system-logs'|'head.clientCard.refresh-open-tasks'|'head.client-phone.storePhone'|'head.client-phone.destroy'|'head.client-phone.edit'|'head.client-phone.update'|'head.client.updatePhone'|'head.client.addPhone'|'head.client.deletePhone'|'head.client.updateAddress'|'head.client-address.create'|'head.client-address.store'|'head.client-address.delete'|'head.client.updateGuarant'|'head.client.createGuarant'|'head.client.updateContact'|'head.client.createContact'|'head.clientCard.approveAttempt'|'head.clientCard.update-employer'|'head.clientCard.refreshPayments'|'head.clientCard.prefillConsultantsOffice'|'head.clientCard.prefillOffices'|'head.clientCard.updateOfficeConsultant'|'head.clientCard.refreshInstallments'|'head.clientCard.refreshTaxes'|'head.clientCard.createNewCcrReport'|'head.clientCard.createNewNoiReport'|'head.clientCard.createNewMvrReport'|'head.clientCard.createNewA4EReport'|'head.clientCard.printDocuments'|'head.clientCard.loans'|'head.clientCard.loadTab'|'head.clientCard.refreshClientCommunication'|'head.clientCard.renderTab'|'head.clientCard.getClientLoansRest'|'head.clientCard.getAllGuarantTypesRest'|'head.clientCard.getAllContactTypesRest'|'head.clientCard.getClientLoanStatisticsRest'|'head.clientCard.getCitiesRest'|'head.clientCard.getAllOfficesRest'|'head.clientCard.getAllPaymentMethodsRest'|'head.clientCard.getManualTemplatesRest'|'head.clientCard.getSaleDecisionsRest'|'head.clientCard.getRefinanceParamsRest'|'head.clientCard.getCalculatorDataByLoanRest'|'head.clientCard.getAllClientOpenTasksRest'|'head.clientCard.getClientLoansLightRest'|'head.clientCard.getClientSmsRest'|'head.clientCard.getAllCcrReportsByPinRest'|'head.clientCard.hasCcrReportsPermissionRest'|'head.clientCard.getNoiReportsRest'|'head.clientCard.hasNoiReportsPermissionRest'|'head.clientCard.getMvrReportsRest'|'head.clientCard.hasMvrReportsPermissionRest'|'head.clientCard.getA4eReportsRest'|'head.clientCard.hasA4eReportsPermissionRest'|'head.clientCard.getClientCommunicationRest'|'head.clientCard.getAllPaymentSourcesRest'|'head.clientCard.getPaymentTableDataRest'|'head.clientCard.getLoanExtensionDataRest'|'head.clientCard.getLoanTaxesRest'|'head.clientCard.calculateExtensionFeeRest'|'head.clientCard.getLoanCommonInformationRest'|'head.clientCard.getLoanDuesRest'|'head.clientCard.getLoanPaidUntilNowRest'|'head.clientCard.getLoanLeftToPayRest'|'head.clientCard.getLoanMaturedAmountsRest'|'head.clientCard.getLoanOverviewRest'|'head.clientCard.getSystemLogsTableDataRest'|'head.clients.sessions'|'head.clients.sessionDelete'|'head.interestTerm.list'|'head.interestTerm.detail'|'head.interestTerm.import'|'head.interestTerm.refresh'|'head.penaltyTerm.list'|'head.penaltyTerm.detail'|'head.penaltyTerm.import'|'head.penaltyTerm.refresh'|'payment.payments.list'|'payment.payments.refresh'|'payment.refundPage'|'payment.refund'|'payment.refundState'|'payment.manual-payment.client-overview'|'payment.manual-payment.payment-overview'|'payment.payment.rollback'|'payment.payment-schedule.get'|'payments.client.search'|'payments.client.get-client-data'|'payments.spread-payment.post'|'payments.save-payment.post'|'payments.edit-payment.post'|'payments.easypay.listRequest'|'payments.easypay.getRequestFilters'|'payments.easypay.setRequestFilters'|'payments.easypay.cleanRequestFilters'|'payments.easypay.refreshRequest'|'payment.paymentsTasks.list'|'payment.paymentsTasks.refresh'|'payment.paymentsTasks.export'|'payment.paymentsTasks.processing'|'payment.paymentsTasks.exit-task'|'payment.paymentsTasks.import'|'payment.paymentsTasks.cancel-task'|'payment.paymentsTasks.cancel-task-and-loan'|'payment.paymentsTasks.approve-outgoing-task'|'payment.paymentsTasks.reSendEasypayRequest'|'payment.paymentTasks.completePaymentTask'|'payments.tax.create'|'payments.tax.addAmount'|'payments.tax.extendLoan'|'payments.tax.deleteTax'|'payments.tax.destroy'|'payments.tax.updateTaxes'|'payments.tax.calculateLoanExtendFee'|'payments.paymentTaskDecision.list'|'payments.paymentTaskDecision.create'|'payments.paymentTaskDecision.store'|'payments.paymentTaskDecision.edit'|'payments.paymentTaskDecision.update'|'payments.paymentTaskDecision.delete'|'payments.paymentTaskDecision.enable'|'payments.paymentTaskDecision.disable'|'payment.manual-payment.index'|'payment.manual-payment.overview'|'payment.manual-payment.storeManualPayment'|'payment.manual-payment.load-loans'|'payment.manual-payment.load-loans-info'|'sales.saleDecision.list'|'sales.saleDecision.create'|'sales.saleDecision.store'|'sales.saleDecision.edit'|'sales.saleDecision.update'|'sales.saleDecision.delete'|'sales.saleDecision.enable'|'sales.saleDecision.disable'|'sales.saleDecisionReason.list'|'sales.saleDecisionReason.create'|'sales.saleDecisionReason.store'|'sales.saleDecisionReason.edit'|'sales.saleDecisionReason.update'|'sales.saleDecisionReason.delete'|'sales.saleDecisionReason.enable'|'sales.saleDecisionReason.disable'|'sales.saleTask.list'|'sales.saleTask.refresh'|'sales.saleTask.getFilters'|'sales.saleTask.setFilters'|'sales.saleTask.cleanFilters'|'sales.processing'|'sales.saleTask.ajaxTimer'|'sales.setOfficeId'|'sales.newAppFromSaleTask'|'sales.newApplication'|'sales.saveNewApplication'|'sales.storeNewCompanyApplication'|'sales.checkClientFromMvr'|'sales.getProductSettingsByProduct'|'sales.getClientLoanData'|'sales.calculateLoanParams'|'sales.getActiveLoans'|'sales.getProductsByOffice'|'sales.getRestAfterRefinance'|'sales.refinance-slider.fetch-refinance-loans-info')} routeEnum
 */
