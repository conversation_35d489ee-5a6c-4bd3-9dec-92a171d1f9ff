import routeAxios from "../routeAxios";

export default new class {
    fetchClientLoans(clientIds) {
        return routeAxios('payment.manual-payment.load-loans', {}, {
            clientIds: clientIds
        });
    }

    fetchClientLoansInfo(selectedLoanIds) {
        return routeAxios('payment.manual-payment.load-loans-info', {}, {
            selectedLoanIds: selectedLoanIds
        });
    }

    spreadPaymentToClientLoans(data) {
        return routeAxios('payment.manual-payment.load-loans-info', {}, data);
    }

    saveSpreadPayment(data) {
        return routeAxios('payment.manual-payment.storeManualPayment', {}, data);
    }
}
