import list from "./list";

/**
 *
 * @param {routeEnum} name
 * @param replacements
 * @returns {null|*}
 */
export default function (name, replacements = {}) {
    if (!list.hasOwnProperty(name)) {
        console.warn('route "' + name + '" is undefined');
        return null;
    }

    let routeItem = list[name][1];
    return routeItem.replace(/{(\w+)}/g, (match, routeKey) => {
        if (!replacements.hasOwnProperty(routeKey)) {
            throw new Error('"' + routeKey + '" in replacements route "' + name + '" is undefined');
        }
        return replacements[routeKey];
    });
}