window._ = require('lodash');

try {
    window.Popper = require('popper.js');
    window.$ = window.jQuery = require('jquery');
    window.Sortable = require('sortablejs/Sortable');
    require('bootstrap');
    require('bootstrap-datepicker');
    require('daterangepicker');
    require('bootstrap-select/js/bootstrap-select');

    require('select2/dist/js/select2.full');

    /// require parsley
    require('parsleyjs');
    require('parsleyjs/dist/i18n/bg');
    window.ParsleyValidator.setLocale('bg');
    window.Parsley.addValidator('dateaftertoday', {
        validateString: function (value) {
            // Parse the input date value into a Date object or use moment.js
            // Here, we'll use moment.js for simplicity
            var inputDate = moment(value, 'DD.MM.YYYY');

            // Check if the input date is after today
            if (inputDate.isValid()) {
                return inputDate.isSameOrAfter(moment(), 'day');
            }

            // If the input date is not valid, return false
            return false;
        },
        messages: {
            bg: "Дата на валидностан на лична карта трябва да е по голяма от днес."
        }
    });

    window.Parsley.addValidator('datebeforetoday', {
        validateString: function (value) {
            // Parse the input date value into a Date object or use moment.js
            // Here, we'll use moment.js for simplicity
            var inputDate = moment(value, 'DD.MM.YYYY');

            // Check if the input date is after today
            if (inputDate.isValid()) {
                return inputDate.isSameOrBefore(moment(), 'day');
            }

            // If the input date is not valid, return false
            return false;
        },
        messages: {
            bg: "Дата на издаване на лична карта трябва да е по-малка от днес."
        }
    });

    window.Parsley.addValidator('amountlargerthantenk', {
        validateString: function (value) {
            // Parse the input date value into a Date object or use moment.js
            // Here, we'll use moment.js for simplicity
            let inputAmount = parseInt(value);
            let paymentMethodId = $('#payment_method_id').val();

            if (paymentMethodId != 3) { // for cash only
                return true;
            }
            if (inputAmount < 10000) {
                return true;
            }

            // If the input date is not valid, return false
            return false;
        },
        messages: {
            bg: "Плащането не може да се приеме в брой, защото исканата сума или общата сума за връщане е над 10 000 лв. Може да се плати само чрез банков превод."
        }
    });
} catch (e) {
    console.log('Error in bootstrap.js');
    console.log(e);
}

/**
 * We'll load the axios HTTP library which allows us to easily issue requests
 * to our Laravel back-end. This library automatically handles sending the
 * CSRF token as a header based on the value of the "XSRF" token cookie.
 */

window.axios = require('axios');
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

import moment from "moment";

window.moment = moment;

import {DateTime} from "luxon";

window.DateTime = DateTime;

import noUiSlider from "nouislider";

window.noUiSlider = noUiSlider;
