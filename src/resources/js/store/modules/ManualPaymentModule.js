import axios from "axios";
import manualPaymentResolver from "../../rest/resolvers/ManualPaymentResolver";

export const ManualPaymentModule = {
    state: () => ({
        loans: []
    }),
    mutations: {
        setClientLoans: function (state, payload) {
            state.loans = payload;
        }
    },
    actions: {
        async fetchClientLoans({state, commit}, $clientIds) {
            return manualPaymentResolver.fetchClientLoans($clientIds)
                .then((resp) => {
                    commit('setClientLoans', resp.data.loans);

                    if (resp.data.officeData.selectedOfficeId) {
                        $('#office_id').val(resp.data.officeData.selectedOfficeId);
                        $('#office_id').selectpicker('refresh');
                    }

                    if (resp.data.officeData.paymentMethods) {
                        $('select[name="bank_account_id"]').html(resp.data.officeData.selectPaymentMethodsHtml);
                        $officePaymentMethods = resp.data.officeData.paymentMethodBankAccount;
                    }

                    if (resp.data.officeData.selectedBankAccountId) {
                        $('#bank_account_id').val(resp.data.officeData.selectedBankAccountId);
                    }

                    if (resp.data.officeData.selectedPaymentMethodId) {
                        $('#payment_method_id').val(resp.data.officeData.selectedPaymentMethodId);
                        $('input[name="payment_method_id"]').trigger('change');

                        //     var paymentMethodId = $('#payment_method_id').val();
                        //     if (parseInt(paymentMethodId) === 3) { // 3 = CASH
                        //         $('#receive_cash').prop('disabled', false);
                        //         $('#document_number').prop('disabled', true).prop('required', false);
                        //         $('#description').prop('disabled', true).prop('required', false);
                        //     } else {
                        //         $('#receive_cash').prop('disabled', true);
                        //         $('#document_number').prop('disabled', false).prop('required', true);
                        //         $('#description').prop('disabled', false).prop('required', true);
                        //     }
                    }
                })
        },
    },

    namespaced: true
};
