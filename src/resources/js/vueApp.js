"use strict";

import {createApp} from "vue";
import SingleProductSlider from "./components/SingleProductSlider.vue";
import ClientLoansTable from "./components/manual-payment/ClientLoansTable.vue";
import store from "./store";
import DynamicProductSlider from "./components/DynamicProductSlider.vue";

let vueJsSliderMountEl = '#vuejs-slider';
let vueJsApp = createApp({
    components: {
        SingleProductSlider,
        DynamicProductSlider
    }
});

//////////////////////////////////////////////////////////////////////////////////////////////////
let manualPaymentPageEl = '#manual-payment-vuejs';
let manualPaymentVueJsApp = createApp({
    components: {
        ClientLoansTable
    },
    data() {
        return {
            paymentMethodId: 0
        };
    },
    watch: {
        paymentMethodId(paymentMethodId) {
            this.$refs.clientLoansTable.checkPaymentMethodId();
        }
    }
});

$(document).ready(function () {
    if ($(vueJsSliderMountEl).length) {
        vueJsApp.mount(vueJsSliderMountEl);
    }

    /// manualPaymentVueJsApp
    if ($(manualPaymentPageEl).length) {
        manualPaymentVueJsApp
            .use(store)
            .mount(manualPaymentPageEl);
    }
});
