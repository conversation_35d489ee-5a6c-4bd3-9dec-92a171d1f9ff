"use strict";

const TIMEZONE = 'Europe/Sofia';
const MOMENT_FORMAT = 'DD.MM.YYYY HH:mm';
const LUXON_FORMAT = 'dd.MM.yyyy HH:mm';

window.DateRangePicker = {
    handleDateRangePicker: function () {
        let $input = $('input[data-daterange-picker="true"]');
        let $minDate = false;
        let $maxDate = false;
        let $maxSpanDate = 1000;

        if ($input.data('min-daterange') !== undefined) {
            $minDate = $input.data('min-daterange');
        }

        if ($input.data('max-daterange') !== undefined) {
            $maxDate = $input.data('max-daterange');
        }

        if ($input.data('max-span-daterange') !== undefined) {
            $maxSpanDate = $input.data('max-span-daterange');
        }

        $input.daterangepicker({
            autoUpdateInput: false,
            minDate: $minDate,
            maxDate: $maxDate,
            maxSpan: {
                days: $maxSpanDate
            },
            locale: {
                cancelLabel: 'Clear',
                format: 'DD-MM-YYYY'
            }
        });
        $input.on('apply.daterangepicker', function (ev, picker) {
            $(this).val(picker.startDate.format('DD-MM-YYYY') + ' - ' + picker.endDate.format('DD-MM-YYYY'));
            // if ($(this).val() != '') {
            //     if (picker.startDate.format('DD-MM-YYYY') == picker.endDate.format('DD-MM-YYYY')) {
            //         $(this).val(picker.startDate.format('DD-MM-YYYY'));
            //     }
            // }
        });

        $input.on('cancel.daterangepicker', function () {
            $(this).val('');
        });
    },
    handleDatePickerFromToday: function () {
        $('input[data-date-picker-from-today="true"]').datepicker({
            autoclose: true,
            startDate: new Date(moment()),
            format: 'dd.mm.yyyy'
        });
    },
    handleDatePickerFromTodayDefaultEmpty: function () {
        $('input[data-date-picker-from-today-default-empty="true"]').datepicker({
            autoclose: true,
            startDate: new Date(moment()),
            format: 'dd.mm.yyyy'
        });
    },
    handleDatePickerToToday: function () {
        $('input[data-date-picker-to-today="true"]').datepicker({
            autoclose: true,
            endDate: new Date(moment()),
            format: 'dd.mm.yyyy'
        });
    },
    handleDatePickerToTodayDefaultEmpty: function () {
        $('input[data-date-picker-to-today-default-empty="true"]').daterangepicker({
            autoUpdateInput: false,
            singleDatePicker: true,
            maxDate: moment(),
            locale: {
                format: 'DD.MM.YYYY'
            }
        }).on('apply.daterangepicker', function (ev, picker) {
            $(this).val(picker.startDate.format('DD.MM.YYYY'));
            $(this).trigger('change');
        }).on('cancel.daterangepicker', function () {
            $(this).val('');
        });
    },
    handleDatePicker: function () {
        $('input[data-date-picker="true"]').daterangepicker({
            singleDatePicker: true,
            autoUpdateInput: false,
            locale: {
                format: 'DD.MM.YYYY'
            }
        }).on('apply.daterangepicker', function (ev, picker) {
            $(this).val(picker.startDate.format('DD.MM.YYYY'));
            $(this).trigger('change');
        });
    },
    handleCallMeLaterPicker: function () {
        const now = DateTime.now().setZone(TIMEZONE);

        $('input[data-date-picker-callmelater="true"]')
            .daterangepicker({
                autoUpdateInput: false,
                singleDatePicker: true,
                timePicker: true,
                timePicker24Hour: true,
                minDate: now.toFormat(LUXON_FORMAT),
                maxDate: now.plus({days: 2}).toFormat(LUXON_FORMAT),
                locale: {
                    format: MOMENT_FORMAT
                }
            })
            .on('apply.daterangepicker', function (ev, picker) {
                $(this).val(picker.startDate.format(MOMENT_FORMAT));
                $(this).trigger('change');
            });
    },

    handleDateTimePicker() {
        const now = DateTime.now().setZone(TIMEZONE);
        const inputEl = $('input[data-date-time-picker="true"]');
        let minDate = now.toFormat(LUXON_FORMAT);
        let maxDate = false;

        if (inputEl.data('min-date')) {
            minDate = inputEl.data('min-date');
        }

        if (inputEl.data('max-date')) {
            maxDate = inputEl.data('max-date');
        }

        inputEl.daterangepicker({
            autoUpdateInput: false,
            singleDatePicker: true,
            timePicker: true,
            timePicker24Hour: true,
            minDate: minDate,
            maxDate: maxDate,
            locale: {
                format: MOMENT_FORMAT
            }
        })
        .on('apply.daterangepicker', function (ev, picker) {
            $(this).val(picker.startDate.format(MOMENT_FORMAT));
            $(this).trigger('change');
        });
    },

    handleYearMonthDatePicker() {
        $('input[data-year-month-picker="true"]').datepicker({
            format: "mm-yyyy",
            startView: "months",
            minViewMode: "months",
            autoclose: true
        });
    },

    init: function () {
        this.handleDateRangePicker();
        this.handleCallMeLaterPicker();
        this.handleDatePicker();
        this.handleDateTimePicker();
        this.handleDatePickerFromToday();
        this.handleDatePickerToToday();
        this.handleDatePickerToTodayDefaultEmpty();
        this.handleDatePickerFromTodayDefaultEmpty();
        this.handleYearMonthDatePicker();
    }
};

$(function () {
    window.DateRangePicker.init();
});
