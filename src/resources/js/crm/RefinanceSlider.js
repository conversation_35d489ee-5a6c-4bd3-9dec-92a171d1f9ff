"use strict";
import refinanceSliderResolver from "../rest/resolvers/RefinanceSliderResolver";

window.RefinanceSlider = {
    sliderAmountEl: [],
    dynamicAmountEl: [],
    refinanceAmount: 0,
    watchRefinanceLoanSelected: function () {

        // document.querySelectorAll('ul#officeProducts > li').forEach(function (el) {
        //     el.addEventListener('click', function () {
        //         window.RefinanceSlider.unCheckAllSelectedLoans();
        //     })
        // });

        $(document).on('click', 'input[name="refinanced_loans[]"]', function () {
            if (!$(this).is(':checked')) {
                return;
            }

            let $formData = $(this).parents('form').first().serialize();
            refinanceSliderResolver
                .fetchRefinanceLoansInfo($formData)
                .then((resp) => {
                    let $errorHtml = document.getElementById('js-errors').innerHTML;
                    if (!resp.data.status) {
                        $errorHtml = $errorHtml.replace(/{message}/g, resp.data.message);
                        $('#refinance-errors').html($errorHtml);

                        // window.RefinanceSlider.unCheckAllSelectedLoans();
                    }

                    if (resp.data.status === true) {
                        window.RefinanceSlider.refinanceAmount = resp.data.originRefinanceAmount;

                        if (window.RefinanceSlider.sliderAmountEl[resp.data.productId] !== undefined) {
                            window.RefinanceSlider.sliderAmountEl[resp.data.productId].set(resp.data.refinanceAmount);
                        }

                        if (window.RefinanceSlider.dynamicAmountEl[resp.data.productId] !== undefined) {
                            window.RefinanceSlider.dynamicAmountEl[resp.data.productId].amount = resp.data.refinanceAmount;
                        }
                    }
                });

        });
    },

    unCheckAllSelectedLoans() {
        $('input[name="refinanced_loans[]"]').prop('checked', false);
    },

    setSettingsOnlineOffice() {

        if (window.installmentProductId) {
            document.querySelector('button[data-target="#slider-' + window.installmentProductId + '"]').click();
        }

        // document
        //     .querySelectorAll('button[data-target^="#slider-"]:not(#slider-' + window.installmentProductId + '):not(#slider-' + window.dynamicProductId + ')')
        //     .forEach(function (row) {
        //         row.classList.add('disabled');
        //     });

        document.querySelectorAll('input[name="refinanced_loans[]"]').forEach(function (row) {
            row.setAttribute('type', 'hidden');
            row.insertAdjacentHTML('afterend', '<input type="checkbox" checked="checked" disabled="disabled"/>');
        });

        let $formData = $('form[name="newAppForm"]').serialize();
        refinanceSliderResolver
            .fetchRefinanceLoansInfo($formData)
            .then((resp) => {
                let $errorHtml = document.getElementById('js-errors').innerHTML;
                if (!resp.data.status) {
                    $errorHtml = $errorHtml.replace(/{message}/g, resp.data.message);
                    $('#refinance-errors').html($errorHtml);
                }

                if (resp.data.status === true) {
                    window.RefinanceSlider.refinanceAmount = resp.data.originRefinanceAmount;

                    if (window.RefinanceSlider.sliderAmountEl[resp.data.productId] !== undefined) {
                        // window.RefinanceSlider.sliderAmountEl[resp.data.productId].set(resp.data.refinanceAmount);
                        window.RefinanceSlider.sliderAmountEl.forEach(slider => {
                            if (parseInt(resp.data.refinanceAmount) < slider.options.range.max) {
                                slider.set(resp.data.refinanceAmount);
                            }
                        });
                    }

                    if (window.RefinanceSlider.dynamicAmountEl[resp.data.productId] !== undefined) {
                        window.RefinanceSlider.dynamicAmountEl[resp.data.productId].amount = resp.data.refinanceAmount;
                    }
                }
            });
    },

    init: function () {
        if ($('#refinance-loans').length > 0) {
            this.watchRefinanceLoanSelected();

            if (window.isOnlineOffice) {
                this.setSettingsOnlineOffice();
            }
        }
    }
};

$(document).ready(function () {
    window.RefinanceSlider.init();
});
