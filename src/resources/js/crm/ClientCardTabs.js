"use strict";
window.ClientCardTabs = {
    handleClientCardTabClick: function () {
        $('body').on('click', '#client-card-tabs li button', function () {
            let $route = $(this).data('route');
            let $target = $(this).data('target');
            // localStorage.setItem('lastActiveClientCard', $target);

            /// load client card-data
            window.ClientCardTabs.handleLoadClientCardTabData($route, $target);
        });
    },

    handleLoadClientCardTabAfterRefresh: function () {
        let $lastActiveClientCard = localStorage.getItem('lastActiveClientCard');
        let $route = '';

        if ($lastActiveClientCard === null) {
            let $button = $('#client-card-tabs li button.active');
            $lastActiveClientCard = $button.data('target');
            $route = $button.data('route');
            window.ClientCardTabs.handleLoadClientCardTabData($route, $lastActiveClientCard);

            return true;
        }

        $('button[data-target="' + $lastActiveClientCard + '"]').trigger('click');

        $route = $('button[data-target="' + $lastActiveClientCard + '"]').data('route');

        /// load tab data
        window.ClientCardTabs.handleLoadClientCardTabData($route, $lastActiveClientCard)
    },

    handleLoadClientCardTabData: function ($route, $tabContainer) {
        if ($route == '' || $route === undefined) {
            return true;
        }

        let $targetContent = $('div[id="' + $tabContainer.replace('#', '') + '"]');
        $targetContent.html($('<div class="spinner-grow"/>'));

        /// load tab data
        axios
            .get($route)
            .then((response) => {
                $targetContent.html(response.data);
            })
            .catch((error) => {

                let defError = error;
                if (error.response && error.response.data.message) {
                    let parsedErr = JSON.parse(error.response.data.message);
                    if (parsedErr.message) {
                        defError = parsedErr.message[0];
                    }
                }

                $targetContent.html(defError);
            });
    },

    handleChangeAccordionToggleButton: function () {
        /// on toggle tab change icon
        $(document).on('hide.bs.collapse show.bs.collapse', e => {
            $(e.target)
                .prev()
                .find('i:last-child')
                .toggleClass('fa-minus-square fa-plus-square');
        });
    },

    /// init function
    init: function () {
        // this.handleLoadClientCardTabAfterRefresh();
        this.handleClientCardTabClick();
        this.handleChangeAccordionToggleButton();
    }
};

$(document).ready(function () {
    window.ClientCardTabs.init();
});
