let createNoiReports = function () {
    let $noiReportContainer = $('div[id="noireports"]');

    $(document)
        .add('a[data-check="noServiceInformationNoi7"]')
        .add('a[data-check="noServiceInformationNoi2"]')
        .add('a[data-check="noServiceInformationNoi51"]')
        .add('a[data-check="noServiceInformationNoi0"]')
        .on('click', function (event) {
            /// don't know why when click to other elements
            /// this function is triggered ask G<PERSON> Nikolov
            if ($(event.target).data('id') === undefined) {
                return true;
            }

            $(event.target).addClass('disabled');
            $(event.target).children('span.spinner-border').removeClass('d-none');


            let $reportType = $(event.target).data('id').toString();

            if ($reportType.indexOf(',') !== 1) {
                window.createNewNoiReportParams.reportType = [$reportType];
            } else {
                window.createNewNoiReportParams.reportType = $reportType.split(',');
            }

            axios
                .post(window.createNewNoiReportRoute, window.createNewNoiReportParams)
                .then((resp) => {
                    $noiReportContainer.html(resp.data);
                });
        });
};

$(document).ready(function () {
    createNoiReports();
});
