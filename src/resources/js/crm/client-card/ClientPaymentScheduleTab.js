window.ClientPaymentScheduleTab = {
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /// earlyRepaymentCalendar(); uses on main tab in Client card
    earlyRepaymentCalendar: function () {
        let $installmentDates = [];
        let $lastDate = null;
        if (window.installmentDates.length) {
            $(window.installmentDates).each(function (index, date) {
                let $date = moment(date).format('YYYY-MM-DD');
                $installmentDates[$date] = $date;
                $lastDate = $date;
            });
        }

        $('.loan-calendar-early-repayment')
            .datepicker({
                format: 'yyyy-mm-dd',
                language: 'bg',
                weekStart: 1,
                startDate: new Date(),
                endDate: new Date($lastDate),
                todayHighlight: true,
                showButtonPanel: false,
                beforeShowDay: function (date) {
                    let $date = moment(date).format('YYYY-MM-DD');
                    let highlight = $installmentDates[$date];

                    if (highlight) {
                        return {
                            classes: 'installment-date', tooltip: 'Installment date'
                        };
                    }
                }
            })
            .on('changeDate', function (event) {
                let $currentDate = $('#calendar-from input[name="currentDate"]').val();
                if (moment(event.date).format('YYYY-M-DD') !== moment($currentDate).format('YYYY-M-DD')) {
                    let $toDate = moment(event.date).format('YYYY-MM-DD');
                    $('#calendar-from input[name="currentDate"]').val($toDate)

                    /// refresh view
                    window.ClientPaymentScheduleTab.loadClientEarlyRepaymentData();
                }
            });
        $('.loan-calendar-early-repayment').datepicker('setDate', $('#calendar-from input[name="currentDate"]').val());
    },

    loadClientEarlyRepaymentData: function () {
        let $form = $('form[id="calendar-from"]').first();
        let $route = $form.attr('action');
        let $data = $form.serialize();
        $data = $data.replace('showSavedPlanBtn=1', 'showSavedPlanBtn=0');

        axios
            .get($route + '?' + $data)
            .then((resp) => {
                let clientPaymentScheduleBox = document.getElementById('main-calendar-div');
                $(clientPaymentScheduleBox.querySelector('div[id="calendar-stats"]')).html(resp.data);
            });
    },

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /// loanCalendar(); uses on payment schedule tab in Client card
    loanCalendar: function () {
        let $currentDateInput = $('#calendar-from input[name="currentDate"]');
        let $installmentDates = [];
        let $lastDate = null;
        if (window.installmentDates.length) {
            $(window.installmentDates).each(function (index, date) {
                let $date = moment(date).format('YYYY-MM-DD');
                $installmentDates[$date] = $date;
                $lastDate = $date;
            });
        }

        $('.loan-calendar')
            .datepicker({
                format: 'yyyy-mm-dd',
                language: 'bg',
                weekStart: 1,
                startDate: new Date(),
                endDate: new Date($lastDate),
                todayHighlight: true,
                showButtonPanel: false,
                beforeShowDay: function (date) {
                    let $date = moment(date).format('YYYY-MM-DD');
                    let highlight = $installmentDates[$date];

                    if (highlight) {
                        return {
                            classes: 'installment-date', tooltip: 'Installment date'
                        };
                    }
                }
            })
            .on('changeDate', function (event) {
                let $currentDate = $currentDateInput.val();
                if (moment(event.date).format('YYYY-M-DD') !== moment($currentDate).format('YYYY-M-DD')) {
                    let $toDate = moment(event.date).format('YYYY-MM-DD');
                    $currentDateInput.val($toDate)

                    /// refresh view
                    window.ClientPaymentScheduleTab.refreshLoanToDate();
                }
            });
        $('.loan-calendar').datepicker('setDate', $currentDateInput.val());
    },

    refreshLoanToDate: function () {
        let $form = $('form[id="calendar-from"]').last();
        let $route = $form.attr('action');
        let $data = $form.serialize();

        axios
            .get($route + '?' + $data)
            .then((resp) => {
                // $('div[id="calendar-stats"]').html(resp.data);
                let clientPaymentScheduleBox = document.getElementById('outer-calendar-div');
                $(clientPaymentScheduleBox.querySelector('div[id="schedule-calendar-stats"]')).html(resp.data);
            });
    },

    handleExtendLoanForm: function () {
        $(document).on('keyup', 'input[name="extendWithDays"]', function () {
            let $reqUrl = $(this).data('calculate-fee-route');
            let $formData = $(this).parents('form').serialize();

            if ($(this).parents('form').parsley().isValid()) {
                axios
                    .get($reqUrl + '?' + $formData)
                    .then(($resp) => {
                        let $extendLoanFeeAmount = parseInt($resp.data.extendLoanFeeAmount);
                        if ($extendLoanFeeAmount > 0) {
                            $('input[name="extendFeeAmount"]').val(($extendLoanFeeAmount / 100).toFixed(2));
                        }
                    });
            }
        });
    },

    init: function () {
        this.handleExtendLoanForm();
    }
};

$(document).ready(function () {
    window.ClientPaymentScheduleTab.init();
});
