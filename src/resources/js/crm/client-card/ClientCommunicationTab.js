window.ClientCommunicationTab = {
    submitFilterForm: function () {
        let $currentTab = $('div[id="communication"]');

        $(document).on('submit', 'form[id="ClientCommunicationFilterForm"]', function (event) {
            event.preventDefault();

            let $route = $(this).attr('action');
            let $formData = $(this).serialize();

            axios
                .get($route + '&' + $formData)
                .then((resp) => {
                    console.log($currentTab);
                    $currentTab.html(resp.data);
                });
        });

        $(document).on('click', 'form[id="ClientCommunicationFilterForm"] a.clear-filters-btn', function (event) {
            event.preventDefault();

            axios
                .get($(this).attr('href'))
                .then((resp) => {
                    $currentTab.html(resp.data);
                });
        });
    },

    init: function () {
        window.Select2Handler.init();

        this.submitFilterForm();
    }
};
