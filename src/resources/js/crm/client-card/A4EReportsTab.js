let createNewA4EReport = function () {
    let $loanHistoryContainer = $('div[id="a4ereports"]');

    $(document).on('click', 'a[data-check="a4eManualReport"]', function (event) {
        $(event.target).addClass('disabled');
        $(event.target).children('span.spinner-border').removeClass('d-none');

        axios
            .post(window.createNewA4EReportRoute, window.createNewA4EReportParams)
            .then((resp) => {
                $loanHistoryContainer.html(resp.data);
            });
    });
};

$(document).ready(function () {
    createNewA4EReport();
});
