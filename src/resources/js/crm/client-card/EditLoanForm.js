"use strict";

// window.EditLoanForm = {
//     getOfficeAdministrators(officeId) {
//         let $getOfficeAdministrators = '/admin/offices/fetch-office-administrators/' + officeId;
//         axios
//             .get($getOfficeAdministrators)
//             .then((resp) => {
//                 if (resp.status === 200) {
//                     $('select[name="administrator_id"]').html(resp.data);
//                 }
//             });
//     },
//     init() {
//         let $this = this;
//         $(document).on('change', '#editLoanForm select[name="office_id"]', function () {
//             let $office_id = $(this).val();
//             $this.getOfficeAdministrators($office_id);
//         })
//     }
// };


// $(document).ready(function () {
//     window.EditLoanForm.init();
// });
