"use strict";
window.ClientPaymentTab = {
    submitFilterForm: function () {
        let $currentTab = $('div[id="payments"]');

        $(document).on('submit', 'form[id="ClientPaymentsFilterForm"]', function (event) {
            event.preventDefault();

            let $route = $(this).attr('action');
            let $formData = $(this).serialize();

            axios
                .get($route + '&' + $formData)
                .then((resp) => {
                    $currentTab.html(resp.data);
                });
        });

        $(document).on('click', 'form[id="ClientPaymentsFilterForm"] a.clear-filters-btn', function (event) {
            event.preventDefault();

            axios
                .get($(this).attr('href'))
                .then((resp) => {
                    $currentTab.html(resp.data);
                });
        });
    },

    init: function () {
        this.submitFilterForm();
    }
};
