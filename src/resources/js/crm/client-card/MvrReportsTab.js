let createNewMvrReport = function () {
    let $mvrReportsContainer = $('div[id="mvrreports"]');

    $(document).on('click', 'a[data-check="noServiceInformationMvr"]', function (event) {
        $(event.target).addClass('disabled');
        $(event.target).children('span.spinner-border').removeClass('d-none');
        
        axios
            .post(window.createNewMvrReportRoute, window.createNewMvrReportParams)
            .then((resp) => {
                $mvrReportsContainer.html(resp.data);
            });
    });
};

$(document).ready(function () {
    createNewMvrReport();
});
