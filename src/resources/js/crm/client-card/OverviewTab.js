"use strict";
import axios from "axios";

window.OverviewTab = {
    initSortable: function () {
        let $this = this;
        if ($('[id^="sortable-"]').length > 0) {
            $('[id^="sortable-"]').each(function (index, element) {
                $this.setSortable(index, element)
            });
        }
    },

    setSortable(index, element) {
        let $this = this;
        return new Sortable(element, {
            // sort: true,
            handle: '.card-title, .cursor-move',
            group: 'shared',
            animation: 200,
            filter: ".modal",
            store: {
                // Sorting acquisition (called during initialization)
                get: function (sortable) {
                    let order = localStorage.getItem(sortable.options.group.name + '-' + index);
                    return order ? order.split('|') : [];
                },

                // Saving the acquired sorting (called each time upon sorting modification)
                set: function (sortable) {
                    let order = sortable.toArray();
                    localStorage.setItem(sortable.options.group.name + '-' + index, order.join('|'));

                    $this.saveBoxesOrder();
                }
            }
        });
    },

    saveBoxesOrder() {
        let $this = this;
        let $client_card_group = $('input[name="client_card_group"]').val();

        $('[id^="sortable-"]').each(function (index, element) {
            let $sortableOrder = $this.setSortable(index, element).toArray();
            axios
                .get('/common/client-card-boxes/save-box-order', {
                    params: {
                        client_card_group: $client_card_group,
                        client_card_box_id: $sortableOrder,
                        client_card_col: index,
                    }
                });
        });
    },

    handleSelectClientLoan() {
        if ($('select[name="select-client-loan"]').length) {
            $('select[name="select-client-loan"]').change(function () {
                let $targetRoute = $(this).val();
                if ($targetRoute != '') {
                    location.replace($targetRoute);
                }
            });
        }
    },

    handleCollectDateBtn() {
        let $btnElement = $('button.btnAddDays');
        
        if ($btnElement.length > 0) {
            $(document).on('click', 'button.btnAddDays', function () {
                let $days = $(this).data('value');
                let $date = moment(moment(), 'DD.MM.YYYY').add('days', $days).format('DD.MM.YYYY');
                $('input[name="show_after"]').val($date);
            });
        }
    },

    init: function () {
        this.handleSelectClientLoan();
        this.handleCollectDateBtn();
    }
};

$(document).ready(function () {
    window.OverviewTab.init();
});
