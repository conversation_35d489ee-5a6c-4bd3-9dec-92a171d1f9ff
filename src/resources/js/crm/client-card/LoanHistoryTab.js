let createNewCcrReport = function () {
    let $loanHistoryContainer = $('div[id="ckrreports"]');

    $(document).on('click', 'a[data-check="noServiceInformationCcr"]', function () {
        $(this).addClass('disabled');
        $(this).children('span.spinner-border').removeClass('d-none');

        axios
            .post(window.createNewCcrReportRoute, window.createNewCcrReportParams)
            .then((resp) => {
                $loanHistoryContainer.html(resp.data);
            });
    });
};

$(document).ready(function () {
    createNewCcrReport();
});
