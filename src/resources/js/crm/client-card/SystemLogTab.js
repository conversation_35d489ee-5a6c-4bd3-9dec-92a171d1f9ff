"use strict";

window.SystemLogTab = {
    systemLogTabContainer: $('div[id="systemlog"]'),
    ajaxPaginator: function () {
        $(document).on('click', 'div.ajax-paginate a[class="page-link"]', function (event) {
            event.preventDefault();
            event.stopPropagation();

            let $route = $(this).attr('href');

            axios
                .get($route)
                .then((resp) => {
                    window.SystemLogTab.systemLogTabContainer.html(resp.data);
                });
        });
    },
    init: function () {
        this.ajaxPaginator();
    }
};

$(document).ready(function () {
    window.SystemLogTab.init();
});