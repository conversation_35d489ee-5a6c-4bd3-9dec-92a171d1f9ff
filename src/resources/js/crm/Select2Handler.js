window.Select2Handler = {
    handleDefaultSelect2: function () {
        let $select2El = $('select[data-s2="true"]');

        if ($select2El.length) {
            $select2El.select2({
                theme: "classic",
                width: "100%",
                minimumResultsForSearch: 10
            });
        }
    },

    handleCitySelect2Search: function () {
        let $select2El = $('select[data-select2-search="true"]');

        if ($select2El.length) {
            $($select2El).select2({
                theme: "classic",
                width: "100%",
                ajax: {
                    url: $($select2El).data('req-route'),
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (cityName, cityId) {
                                return {
                                    text: cityName,
                                    id: cityId
                                }
                            })
                        };
                    }
                }
            });
        }
    },

    handleBootstrapSelectPicker: function () {
        let $bootstrapSelectPicker = $('select[data-boostrap-selectpicker="true"]');

        if ($bootstrapSelectPicker.length) {
            $($bootstrapSelectPicker).selectpicker({
                width: "100%"
            });
        }
    },

    handleBootstrapLiveSearchSelectPicker: function () {
        let $bootstrapSelectPicker = $('select[data-live-search="true"]');

        if ($bootstrapSelectPicker.length) {
            $($bootstrapSelectPicker).selectpicker({
                width: "100%"
            });
        }
    },

    /// init function
    init: function () {
        this.handleDefaultSelect2();
        this.handleCitySelect2Search();

        this.handleBootstrapSelectPicker();
        this.handleBootstrapLiveSearchSelectPicker();
    }
};

$(document).ready(function () {
    window.Select2Handler.init();
});
