"use strict";
window.Dashboard = {
    filterForm: '',
    showModalById(modalId, text = null) {
        if (text !== null) {
            $(modalId).find('div.modal-body').html(text);
        }

        $(modalId).modal('show');
    },
    handleConfirmDialog: function () {
        $('.confirm').each(function (index, $el) {
            if ($el.tagName === 'A') {
                $($el).on('click', $el, function (event) {
                    event.preventDefault();
                    let $confirmMessage = $($el).data('confirm');
                    let $targetRoute = $($el).attr('href');

                    if (confirm($confirmMessage) === true) {
                        location.replace($targetRoute);
                    }

                    return false;
                });
            } else {
                $($el).on('click', $el, function (event) {
                    event.preventDefault();
                    let $confirmMessage = $($el).data('confirm');
                    let $targetRoute = $($el).parent('form').attr('action');
                    let $form = $($el).parent('form');

                    if (confirm($confirmMessage) === true) {
                        $form[0].submit();
                    }

                    return false;
                });
            }
        });
    },

    handleShowOnHover: function () {
        $('.show-on-hover').each(function (index, $el) {
            $($el).parent().hover(
                function () {
                    $($el).css({"display": "block"});
                },
                function () {
                    $($el).css({"display": "none"});
                }
            );
        });
    },

    handleAjaxUpdateForm: function () {
        $('.ajax-update-create').each(function (index, $el) {
            $($el).on('click', $el, function (event) {
                event.preventDefault();
                let $reqRoute = $(this).attr('href');
                let $targetModal = $(this).data('target');

                /// get edit form
                axios
                    .get($reqRoute)
                    .then((resp) => {
                        $($targetModal).remove();
                        $('body').prepend(resp.data);

                        let $forms = $($targetModal).find('form');
                        $($forms).each(function (index, $form) {
                            $($form).parsley();
                        })

                        $($targetModal).modal('show');

                        /// re-init plugins
                        window.Select2Handler.init();
                        window.DateRangePicker.init();
                    });
            });
        });
    },

    submitAjaxForm: function (formId, $context, callback = null) {
        let $method = $('#' + formId).attr('method');
        let $route = $('#' + formId).attr('action');
        let $formData = new FormData(document.getElementById(formId));
        let $container = $($context).parent().prev('div.modal-body');

        switch ($method) {
            case 'POST':
                axios
                    .post($route, $formData)
                    .then(($resp) => {
                        if ($resp.data.status === true) {
                            if ($resp.data.redirect) {
                                window.location.href = $resp.data.redirect;
                            } else {
                                location.reload();
                            }
                        }
                        if ($resp.data.status === false) {
                            $($container).find('div.alert').remove();
                            let $msgBox = $('<div class="alert alert-danger">').text($resp.data.message);
                            $($container).prepend($msgBox);

                            return false;
                        }
                    })
                    .catch(function ($error) {
                        $($container).find('div.alert').remove();
                        let $msgBox = $('<div class="alert alert-danger">').text($error);
                        $($container).prepend($msgBox);
                        return false;
                    });
                break;

            case 'GET':
                axios
                    .get($route, {
                        params: $formData
                    })
                    .then(($resp) => {
                        if ($resp.data.status === true) {
                            location.reload();
                        }

                        if ($resp.data.status === false && callback !== null) {
                            $($container).find('div.alert').remove();
                            let $msgBox = $('<div class="alert alert-danger">').text($resp.data.message);
                            $($container).prepend($msgBox);
                            return false;
                        }
                    })
                    .catch(function ($error) {
                        $($container).find('div.alert').remove();
                        let $msgBox = $('<div class="alert alert-danger">').text($resp.data.message);
                        $($container).prepend($msgBox);
                        return false;
                    });
                break;
        }
    },

    handleRefreshTableInfo: function ($reqUrl, $tableId, $timeInterval = 3000) {
        let $this = this;
        let $url = new URL(window.location);
        setInterval(function () {
            let $filters = [];
            if ($this.filterForm !== '') {
                $filters = $($this.filterForm).serializeArray();
            }

            if ($url.searchParams.has('page')) {
                $filters.push({
                    name: 'page',
                    value: $url.searchParams.get('page')
                });
            }

            $.get($reqUrl, $filters, function ($resp) {
                if ($resp === 'Unauthorized') {
                    location.replace('/login');
                }

                $($tableId).html($resp);

                //// re-init copy function
                window.Dashboard.copyText();
            });

        }, $timeInterval);
    },

    handleChangePaginationLimit() {
        $(document).on('change', 'select[name="pageLimit"]', function () {
            let $routeName = $(this).data('route-name');
            let $reqUrl = '/admin/set-pagination-limit/' + $routeName + '/' + $(this).val();

            $.get($reqUrl, function () {
                let url = new URL(location.href);
                let params = new URLSearchParams(url.search);
                params.delete('page');
                url.search = params.toString();
                location.href = url.toString();
            });
        });
    },

    copyText() {
        if (document.querySelectorAll('.copyButton').length) {
            document.querySelectorAll('.copyButton').forEach(function (row) {
                $(row).tooltip({
                    trigger: "click",
                });

                row.addEventListener('click', function (event) {

                    let textToCopy = $(event.target).text();
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(textToCopy);
                        $(event.target).tooltip('show');
                        setTimeout(function () {
                            $(event.target).tooltip('hide');
                        }, 1000);
                        console.log("Text copied to clipboard: " + $(event.target).text());
                    } else {
                        // Use the 'out of viewport hidden text area' trick
                        const textArea = document.createElement("textarea");
                        textArea.value = textToCopy;

                        // Move textarea out of the viewport so it's not visible
                        textArea.style.position = "absolute";
                        textArea.style.left = "-999999px";

                        document.body.prepend(textArea);
                        textArea.select();

                        try {
                            document.execCommand('copy');
                            $(event.target).tooltip('show');
                            setTimeout(function () {
                                $(event.target).tooltip('hide');
                            }, 1000);

                            console.log("Text copied to clipboard: " + $(event.target).text());
                        } catch (error) {
                            console.error(error);
                        } finally {
                            textArea.remove();
                        }
                    }

                    // // Use the Clipboard API to copy the text to the clipboard
                    // navigator.clipboard.writeText($(event.target).text())
                    //     .then(() => {
                    //         $(event.target).text('Copied: ' + $(event.target).text());
                    //
                    //         console.log("Text copied to clipboard: " + $(event.target).text());
                    //     })
                    //     .catch((err) => {
                    //         console.error("Unable to copy text to clipboard", err);
                    //     });
                });
            });
        }
    },

    init: function () {
        this.handleConfirmDialog();
        this.handleShowOnHover();
        this.handleAjaxUpdateForm();
        this.handleChangePaginationLimit();
        this.copyText();
    }
};

$(document).ready(function () {
    window.Dashboard.init();
});
