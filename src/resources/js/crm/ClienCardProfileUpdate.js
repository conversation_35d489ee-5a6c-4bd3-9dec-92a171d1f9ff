"use strict";


window.clientModalValidate = class clientModalValidate {
    _dateFormat = 'DD.MM.YYYY';

    _mvrGuarantErrorMsg = '';
    _guarantMvrCheckUrl = '';

    constructor(mvrGuarantErrorMsg, guarantMvrCheckUrl) {
        this._mvrGuarantErrorMsg = mvrGuarantErrorMsg;
        this._guarantMvrCheckUrl = guarantMvrCheckUrl;
    }

    init() {
        let self = this;
        $('#clientEditFormModal').on('shown.bs.modal', function () {
            self.mvrButton();
            self.submitFormClientEdit();
        })
    }

    submitFormClientEdit() {
        let self = this;
        $('#clientEditFormModal form').on('submit', function (e) {
            let form = e.target

            let errors = [];
            $('.custom-error-client-edit').remove();

            if (!form.checkValidity()) {
                errors.push(false);
                form.reportValidity()
            }

            errors.push(self.validateClientIssueDate())
            errors.push(self.validateClientValidDate())
            errors.push(self.validateClientIdCard())

            errors = errors.filter((value) => !value)

            if (errors.length > 0) {
                e.preventDefault();
            }
        })
    }

    htmlErrorClientEdit(message) {
        return `<div class="text-danger custom-error-client-edit">${message}</div>`
    }

    validateClientIssueDate() {
        let self = this;

        const el = $('#clientEditFormModal [name^="client_idcard[issue_date]"]');
        let formDate = moment(el.val(), self._dateFormat);
        let checkedDate = moment().startOf('day').format(self._dateFormat);

        if (formDate > checkedDate) {
            el.after(self.htmlErrorClientEdit('Датата трябва да е по-малка или равна от текущата'));
            return false;
        }
        return true;
    }

    validateClientValidDate() {
        let self = this
        const el = $('#clientEditFormModal [name^="client_idcard[valid_date]"]');

        let formDate = moment(el.val(), self._dateFormat);
        let checkedDate = moment().startOf('day');

        if (formDate < checkedDate) {
            el.after(self.htmlErrorClientEdit('Датата трябва да е по-голяма или равна на текущата дата'));
            return false;
        }
        return true;
    }

    validateClientIdCard() {
        let self = this
        const el = $('#clientEditFormModal [name^="client_idcard[idcard_number]"]');
        let val = el.val();
        if (!val) {
            el.after(self.htmlErrorClientEdit('Няма номер за лична карта'));
            return false;
        }

        let stringLength = el.val().length;

        if (stringLength < 9 || stringLength > 10) {
            el.after(self.htmlErrorClientEdit('номерът на личната карта трябва да бъде 9 или 10 знака'));
            return false;
        }
        return true;
    }

    mvrError(elementId, html) {
        $(`${elementId} #cstm-danger-alert-guarant`).slideDown('slow');
        $(`${elementId} #cstm-danger`).show('slow');
        $(`${elementId} #cstm-danger-alert-guarant .message`).html(html);
        setTimeout(function () {
            $(`${elementId} #cstm-danger-alert-guarant`).slideUp('slow');
        }, 5000);
    }

    objectToDotNotation(obj) {
        var res = {};
        (function recurse(obj, current) {
            for (var key in obj) {
                var value = obj[key];
                var newKey = (current ? current + "." + key : key);  // joined key with dot
                if (value && typeof value === "object") {
                    recurse(value, newKey);  // it's a nested object, so do it again
                } else {
                    res[newKey] = value;  // it's not an object, so set the property
                }
            }
        })(obj);
        return res;

    }

    ObjectToBraceNotation(obj) {
        var res = {};
        (function recurse(obj, current) {
            for (var key in obj) {
                var value = obj[key];
                var newKey = (current ? current + "[" + key + ']' : key);  // joined key with dot
                if (value && typeof value === "object") {
                    recurse(value, newKey);  // it's a nested object, so do it again
                } else {
                    res[newKey] = value;  // it's not an object, so set the property
                }
            }
        })(obj);
        return res;

    }

    mvrButton() {
        let self = this;
        $("#addNewGuarantButton").click(function () {
            //searchByPinGuarant($('#guarant1 #guarant_pin').parent());
            $('#cstm-danger-alert-guarant').hide();
        })

        $(document).on('click', '.guarant-mvr-check', function (event) {
            event.preventDefault();

            const element = jQuery(this);
            let elementId = '#' + element.attr('data-container-id');

            const fields = {
                'idcard_number': 'client_idcard[idcard_number]',
                'pin': 'client_idcard[pin]',
            };

            const data = {};

            const state = {
                hasError: false,
            };

            Object.keys(fields).forEach((key, index) => {

                const selector = `${elementId} [name^="${fields[key]}"]`;
                const element = jQuery(selector);

                if (element.length > 0) {

                    const value = element.val().trim();

                    if (value.length > 0) {
                        const fieldDataKey = fields[key];

                        data[fieldDataKey] = value;
                    } else if (
                        value.length === 0 &&
                        !state.hasError
                    ) {
                        state.hasError = true;
                        self.mvrError(elementId, self._mvrGuarantErrorMsg);
                    }
                } else {
                    state.hasError = true;
                    self.mvrError(elementId, self._mvrGuarantErrorMsg);
                }
            });

            if (state.hasError) {
                return;
            }

            $.ajax({
                url: self._guarantMvrCheckUrl,
                method: 'GET',
                data: data,
                dataType: 'json',
                success: function (data) {
                    if (data.error) {
                        mvrError(elementId, data.error);
                        return;
                    }

                    const fillableData = self.ObjectToBraceNotation(data);

                    if (!fillableData) {
                        return;
                    }

                    Object.keys(fillableData).forEach((key, index) => {
                        let syn = key;
                        if (syn === 'client_idcard[idcard_issued_id]') {
                            syn = 'client_idcard[issue_by_city_id]'
                        }

                        const selector = `${elementId} [name^="${syn}"]`;
                        const element = jQuery(selector);

                        if (element.length > 0) {

                            let value = fillableData[key];
                            if (key === 'client_idcard[issue_date]' || key === 'client_idcard[valid_date]') {
                                value = moment(value).format(self._dateFormat)
                            }

                            element.val(value).trigger('change');
                        }
                    });
                },
                error: function (response) {
                    const idCardErrorContainer = $(`${elementId} #cstm-danger-alert-guarant`);

                    let {message} = response.responseJSON;
                    message = JSON.parse(message);
                    if (typeof message === 'object') {
                        let messages = [];
                        for (const [key, value] of Object.entries(message)) {
                            messages.push(value)
                        }
                        message = messages.join('<br/>');
                    }
                    self.mvrError(elementId, message);

                    // showErrorMessage(
                    //     idCardErrorContainer,
                    //     message,
                    //     window.SalesNewApplicationHelper.parent.config.newApplicationDefaultDelayTimeout
                    // );
                }
            });

            return undefined;
        });
    }
}
