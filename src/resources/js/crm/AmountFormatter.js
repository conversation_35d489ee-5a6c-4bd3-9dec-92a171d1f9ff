"use strict";

window.AmountFormatter = {
    handleAmountFormatter: function () {
        let $input = $('input[data-amount-formatter="true"]');

        if (!$input.length) {
            return;
        }

        $input.focusout(function () {
            let $number = parseFloat($(this).val());

            if (!isNaN($number)) {
                $(this).val($number.toFixed(2));
            }
        });
    },

    init: function () {
        this.handleAmountFormatter();
    }
};

$(document).ready(function () {
    window.AmountFormatter.init();
});
