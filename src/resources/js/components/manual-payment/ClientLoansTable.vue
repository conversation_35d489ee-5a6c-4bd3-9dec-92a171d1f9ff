<template>
    <div v-if="this.loans">
        <table class="table table-sm table-bordered" id="manual-payment-select-clients">
            <thead>
            <tr>
                <th></th>
                <th>ЕГН</th>
                <th>Име</th>
                <th>Заем №</th>
                <th>Усвоена сума</th>
                <th>Оставащо по кредита</th>
                <th>Сума при удължаване</th>
                <th>Сума предсрочно пог.</th>
                <th>Просрочени дни</th>
                <th>Дължима сума</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="loan in this.loans">
                <td class="text-center">
                    <input type="checkbox"
                           class="cursor-pointer"
                           :name="'selectedLoanIds['+loan.loanId+']'"
                           :value="loan.loanId"
                           :key="loan.loanId"
                           v-model="selectedLoanIds"
                    />
                </td>
                <td>{{ loan.pin }}</td>
                <td>{{ loan.clientFullName }}</td>
                <td>{{ loan.loanId }}</td>
                <td>{{ loan.approvedAmount }}</td>
                <td>{{ loan.regularRepaymentAmount }}</td>
                <td>{{ loan.earlyRepaymentAmount }}</td>
                <td>{{ loan.extensionAmount }}</td>
                <td>{{ loan.currentOverdueDays }}</td>
                <td>{{ loan.accruedTotalAmount }}</td>
            </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
import {mapActions, mapState} from "vuex";
import manualPaymentResolver from "../../rest/resolvers/ManualPaymentResolver";

export default {
    name: "ClientLoansTable",
    data() {
        return {
            selectedLoanIds: [],
            createManualPaymentFormEl: 'form#CreateManualPaymentForm',
            findClientEl: 'select[data-find-client="true"]'
        }
    },
    mounted() {
        this.handleSelectClient();

        /// init parsley validation
        $(this.createManualPaymentFormEl).parsley();

        let $vueThis = this;
        // $(document).on('change', 'select.loanAction', function () {
        //    $vueThis.spreadPaymentAmount();
        // });

        $('input[name="payment_method_id"]').change((event) => {
            this.$root.paymentMethodId = parseInt(event.target.value);
        });

        $(document).on('click', 'button.select2-selection__choice__remove', () => {
            this.selectedLoanIds = [];
        });
    },
    computed: {
        ...mapState({
            loans: state => state.manualPayments.loans,
        })
    },
    watch: {
        /// watch for state ids, when select one or more loans
        /// load via axios info
        selectedLoanIds: function () {
            // this.checkPaymentMethodId();
            this.fetchClientLoansInfo();
        }
    },
    methods: {
        ...mapActions({
            fetchClientLoans: 'manualPayments/fetchClientLoans'
        }),
        fetchClientLoansInfo() {
            let $container = $('div[id="client-loans-info"]');
            let $reqUrl = $container.data('load-loans-route');
            if (!$container.length) {
                return alert('No available container div[id="client-loans-info"]');
            }
            if ($reqUrl === '') {
                return alert('Invalid route for loading loans info');
            }

            if (!this.selectedLoanIds.length) {
                return $container.html("");
            }

            manualPaymentResolver.fetchClientLoansInfo(this.selectedLoanIds)
                    .then((resp) => {
                        $container.html(resp.data);
                    });
        },
        spreadPaymentAmount() {
            $('button[name="savePayment"]').attr('disabled', 'disabled');

            let $container = $('div[id="client-loans-info"]');
            let $btn = $('a[data-spread-btn="true"]');
            let $createManPaymentForm = $(this.createManualPaymentFormEl);
            let $data = {};

            if (this.selectedLoanIds.length === 0) {
                $('button[name="savePayment"]').removeAttr('disabled');
                return alert('Select loan for spread amount');
            }

            $($createManPaymentForm.serializeArray()).each(function (key, row) {
                $data[row.name] = row.value;
            });

            $btn.addClass('disabled');
            return manualPaymentResolver.spreadPaymentToClientLoans($data)
                    .then((resp) => {
                        $container.html(resp.data);

                        $btn.removeClass('disabled');
                        $('button[name="savePayment"]').removeAttr('disabled');
                    })
                    .catch((resp) => {
                        console.error(resp);
                    });
        },
        async saveSpreadPayment() {
            $('button[name="savePayment"]').attr('disabled', 'disabled');

            /// before save spread payment amount again to update input values
            await this.spreadPaymentAmount();

            let $form = $(this.createManualPaymentFormEl);
            if (!$form.parsley().isValid()) {
                $('button[name="savePayment"]').removeAttr('disabled');
                return false;
            }

            /// check for select loan
            if (this.selectedLoanIds.length === 0) {
                $('button[name="savePayment"]').removeAttr('disabled');
                return alert('Please select one or more loans.');
            }

            let $data = new FormData($form[0]);

            if (this.selectedLoanIds.length) {
                $(this.selectedLoanIds).each(function (key, loanId) {
                    $data.append('loans[]', loanId);
                });
            }

            $('button[name="savePayment"]').attr('disabled', 'disabled');
            $('button[name="savePayment"]').children('i:first').removeClass('fa-save');
            $('button[name="savePayment"]').children('i:first').addClass('fa-spinner fa-spin');
            manualPaymentResolver.saveSpreadPayment($data)
                    .then(resp => {

                        if (resp.data.status === false) {
                            alert('Error: ' + resp.data.message);

                            $('button[name="savePayment"]').removeAttr('disabled');
                            $('button[name="savePayment"]').children('i:first').removeClass('fa-spinner fa-spin');
                            $('button[name="savePayment"]').children('i:first').addClass('fa-save');

                            return false;
                        }

                        // print: kasov order ili kvitanciya
                        if (resp.data.downloadRoute && resp.data.downloadRoute.length > 0) {
                            let tabUrl = resp.data.downloadRoute;
                            window.open(tabUrl, '_blank', 'noreferrer');
                        }

                        if (resp.data.status === true && resp.data.reload === true) {
                            window.location.replace('/payments/manual-payment');
                        }
                    })
                    .catch((resp) => {
                        $('button[name="savePayment"]').removeAttr('disabled');
                        $('button[name="savePayment"]').children('i:first').removeClass('fa-spinner fa-spin');
                        $('button[name="savePayment"]').children('i:first').addClass('fa-save');

                        console.log('catch');
                        console.log(resp);
                        console.log(resp.data);
                        console.log(resp.data.message);
                    });

            console.log('saveSpreadPayment');
        },
        checkPaymentMethodId() {
            if (this.isCashPaymentMethod() && this.selectedLoanIds.length > 1) {
                return alert('Error, on cash payment, only one loan!!!!!');
            }
        },
        isCashPaymentMethod() {
            return this.$root.paymentMethodId === 3;
        },
        handleSelectClient() {
            let $vueThis = this;
            let $findClientsEl = $('select[data-find-client="true"]');

            $findClientsEl.select2({
                width: "100%",
                ajax: {
                    url: $($findClientsEl).data('req-route'),
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: '[' + item.pin + ']: ' + item.name + ' - ' + item.phone,
                                    id: item.client_id
                                }
                            })
                        };
                    }
                }
            });
            $findClientsEl.on('select2:select', function (event) {
                let $clientIds = $(this).val();
                $vueThis.fetchClientLoans($clientIds)
                        .then(function () {
                            if ($vueThis.loans.length === 1) {
                                $('input[type="checkbox"][value="' + $vueThis.loans[0]['loanId'] + '"]').trigger('click');
                            }
                        });
            });

            if ($('select[data-find-client="true"]>option').length) {
                $findClientsEl.val($('select[data-find-client="true"]>option').val());
                $findClientsEl.trigger('change.select2');
                $findClientsEl.trigger('select2:select');
            }

            //// after finaly removed fetch again outstanding client loans data
            $findClientsEl.on('select2:unselect', function (event) {
                let $clientIds = $(this).val();
                $vueThis.fetchClientLoans($clientIds);

                $vueThis.selectedLoanIds = [];
            });
        },
    }
}
</script>
