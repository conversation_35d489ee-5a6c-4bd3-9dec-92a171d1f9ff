<template>
  <div>

    <div class="col-lg-12 mt-5 pb-5 mb-5">
      <div class="mb-3">
        Сума:
        <strong :id="'selectedAmount-'+ this.productid"></strong>
      </div>
      <div :id="'sliderAmount-'+ this.productid"></div>
      <!-- End ./sliderAmount -->
    </div>
    <div class="row"></div>

    <div class="col-lg-12 mt-5 pb-5 mb-5">
      <div class="mb-3">
        Период:
        <strong :id="'selectedPeriod-'+ this.productid"></strong>
      </div>
      <div :id="'sliderPeriod-'+ this.productid"></div>
      <!-- End ./sliderPeriod -->
    </div>
    <div class="row"></div>

    <div class="col-lg-12 mt-5 pb-5 mb-5">
      <div class="mb-3">
        Отстъпка:
        <strong :id="'selectedDiscount-'+this.productid"></strong>
      </div>
      <div :id="'sliderDiscount-'+this.productid"></div>
      <!-- End ./sliderDiscount -->
    </div>
    <hr>
    <div class="text-dark overflow-x-scroll">
      <table class="table table-sm table-bordered">
        <tr v-if="this.refinanceAmount"
            :class="{'bg-success': (this.sliderAmount - this.refinanceAmount) > 0, 'bg-danger': (this.sliderAmount - this.refinanceAmount) <= 0}">
          <td colspan="3">Сума след рефинансиране</td>
          <td>{{ (this.sliderAmount - this.refinanceAmount).toFixed(2) }}</td>
        </tr>
        <tr>
          <td>Сума</td>
          <td>{{ this.sliderAmount }}</td>
          <td>Период</td>
          <td>{{ this.sliderPeriod }}</td>
        </tr>
        <tr>
          <td>Брой вноски</td>
          <td>{{ this.numberOfInstallments }}</td>
          <td>Лихва</td>
          <td>
            {{ this.interestAmount }}
            <span v-if="(this.sliderDiscount > 0)" class="text-success">
                            -{{ this.interestAmountDiscount }}
                        </span>
          </td>
        </tr>
        <tr>
          <td>Вноска</td>
          <td>
            {{ this.installmentAmount }}
            <span v-if="(this.sliderDiscount > 0)" class="text-success">
                            -{{ this.installmentAmountDiscount }}
                        </span>
          </td>
          <td>Неустойка</td>
          <td>
            {{ this.penaltyAmount }}
            <span v-if="(this.sliderDiscount > 0)" class="text-success">
                            -{{ this.penaltyAmountDiscount }}
                        </span>
          </td>
        </tr>

        <tr>
          <td>Общо за връщане</td>
          <td>
            {{ this.totalReturnAmount }}
            <span v-if="(this.sliderDiscount > 0)" class="text-success">
                            -{{ this.totalReturnAmountDiscount }}
                        </span>
          </td>
          <td>Общо оскъпяване</td>
          <td>
            {{ this.totalIncreasedAmount }}
            <span v-if="(this.sliderDiscount > 0)" class="text-success">
                            -{{ this.totalIncreasedAmountDiscount }}
                        </span>
          </td>
        </tr>
      </table>
    </div>

  </div>
</template>

<script>
export default {
  name: "SingleProductSlider",
  props: {
    'productid': Number,
    'loanid': Number,
    clientPin: {
      type: String
    },
    phone: {
      type: String
    }
  },
  data() {
    return {
      productSettings: {
        isJuridical: 'individual',
        amount: {
          default: 0,
          start: 0,
          max: 0,
          label: ' лв'
        },
        period: {
          default: 0,
          start: 0,
          max: 0,
          label: ' дни'
        },
        discount: {
          default: 0,
          start: 0,
          max: 100,
          label: ' %'
        }
      },
      sliderAmountEl: null,
      sliderPeriodEl: null,
      sliderDiscountEl: null,
      /// slider options
      sliderAmount: 0,
      sliderPeriod: 0,
      sliderDiscount: 0,

      /// calculated loan amounts
      numberOfInstallments: 1,
      installmentAmount: 0,
      installmentAmountDiscount: 0,
      interestAmount: 0,
      interestAmountDiscount: 0,
      penaltyAmount: 0,
      penaltyAmountDiscount: 0,
      totalReturnAmount: 0,
      totalReturnAmountDiscount: 0,
      totalIncreasedAmount: 0,
      totalIncreasedAmountDiscount: 0,
      refinanceAmount: 0,
    };
  },
  mounted() {
    this.setLoanFormData();
    this.fetchProductSettings();
    this.setActiveProductTab();

    document.querySelectorAll('input[type="checkbox"][name="refinanced_loans[]"]').forEach((input) => {
      input.addEventListener('click', () => {
        if (!input.checked) {
          this.refinanceAmount = 0;
        }
      });
    });
  },
  methods: {
    async fetchProductSettings() {
      const $resp = await axios
          .get(window.fetchProductSettingsRoute, {
            params: {
              productId: this.productid,
              loanId: (window.loanId > 0) ? window.loanId : null,
              clientPin: this.clientPin,
              phone: this.phone,
            }
          });

      this.productSettings = $resp.data;
      this.refinanceAmount = $resp.data.refAmount;

      /// after fetch product settings
      /// init slider and calculate loan
      this.initNoUiSlider();
      this.calculateLoan();
    },
    async calculateLoan() {
      let $resp = await axios
          .get(window.calculateLoanRoute, {
            params: {
              productId: this.productid,
              loanId: this.loanid,
              clientPin: this.clientPin,
              principle: this.sliderAmount,
              period: this.sliderPeriod,
              discount: this.sliderDiscount,
            }
          });

      this.numberOfInstallments = $resp.data.numberOfInstallments;
      this.installmentAmount = $resp.data.installmentAmount;
      this.installmentAmountDiscount = $resp.data.installmentAmountDiscount;
      this.interestAmount = $resp.data.interestAmount;
      this.interestAmountDiscount = $resp.data.interestAmountDiscount;
      this.penaltyAmount = $resp.data.penaltyAmount;
      this.penaltyAmountDiscount = $resp.data.penaltyAmountDiscount;
      this.totalReturnAmount = $resp.data.totalReturnAmount;
      this.totalReturnAmountDiscount = $resp.data.totalReturnAmountDiscount;
      this.totalIncreasedAmount = $resp.data.totalIncreasedAmount;
      this.totalIncreasedAmountDiscount = $resp.data.totalIncreasedAmountDiscount;

      /// for set calculated values
      this.setActiveProductTab();
    },
    buildAmountSlider: function () {
      let $this = this;
      let $slider = noUiSlider.create(this.sliderAmountEl, {
        start: this.productSettings.amount.default,
        connect: 'lower',
        step: this.productSettings.amount.step,
        range: {
          min: $this.productSettings.amount.start,
          max: $this.productSettings.amount.max
        },
        format: {
          // 'to' the formatted value. Receives a number.
          to: function (value) {
            $this.sliderAmount = value.toFixed(2);
            return value.toFixed(2) + ' ' + $this.productSettings.amount.label;
          },
          // 'from' the formatted value.
          // Receives a string, should return a number.
          from: function (value) {
            return value;
          }
        },
        pips: {mode: 'count', values: 5}
      });

      window.RefinanceSlider.sliderAmountEl[this.productid] = $slider;

      this.sliderAmountEl.noUiSlider.on('update', function (value) {
        if (window.RefinanceSlider.refinanceAmount > 0) {
          $this.refinanceAmount = window.RefinanceSlider.refinanceAmount;
        }

        $('#selectedAmount-' + $this.productid).html(value[0]);
      });
      this.sliderAmountEl.noUiSlider.on('set', function (value) {
        $this.calculateLoan();
      });
    },
    buildPeriodSlider: function () {
      let $this = this;
      noUiSlider.create(this.sliderPeriodEl, {
        start: this.productSettings.period.default,
        connect: 'lower',
        step: 1,
        pips: {mode: 'count', values: 5},
        range: {
          min: $this.productSettings.period.start,
          max: $this.productSettings.period.max
        },
        format: {
          // 'to' the formatted value. Receives a number.
          to: function (value) {
            return Math.round(value) + ' ' + $this.productSettings.period.label;
          },
          // 'from' the formatted value.
          // Receives a string, should return a number.
          from: function (value) {
            return value;
          }
        }
      });

      this.sliderPeriodEl.noUiSlider.on('update', function (value) {
        $this.sliderPeriod = value[0].match(/\d/g).join('');
        $('#selectedPeriod-' + $this.productid).html(value[0]);
      });
      this.sliderPeriodEl.noUiSlider.on('set', function (value) {
        $this.calculateLoan();
      });
    },
    buildDiscountSlider: function () {
      let $this = this;
      noUiSlider.create(this.sliderDiscountEl, {
        start: $this.productSettings.discount.default,
        connect: 'lower',
        step: 1,
        pips: {mode: 'count', values: 5},
        range: {
          min: $this.productSettings.discount.start,
          max: $this.productSettings.discount.max
        },
        format: {
          // 'to' the formatted value. Receives a number.
          to: function (value) {
            return Math.round(value) + ' ' + $this.productSettings.discount.label;
          },
          // 'from' the formatted value.
          // Receives a string, should return a number.
          from: function (value) {
            return value;
          }
        }
      });

      this.sliderDiscountEl.noUiSlider.on('update', function (value) {
        $this.sliderDiscount = value[0].match(/\d/g).join('');
        $('#selectedDiscount-' + $this.productid).html(value[0]);
      });
      this.sliderDiscountEl.noUiSlider.on('set', function (value) {
        $this.calculateLoan();
      });
    },
    initNoUiSlider: function () {
      this.sliderAmountEl = document.getElementById('sliderAmount-' + this.productid);
      this.sliderPeriodEl = document.getElementById('sliderPeriod-' + this.productid);
      this.sliderDiscountEl = document.getElementById('sliderDiscount-' + this.productid);

      this.buildAmountSlider();
      this.buildPeriodSlider();
      this.buildDiscountSlider();
    },
    setActiveProductTab: function () {
      let $productId = parseInt($('input[name="loan[product_id]"]').val());
      if (isNaN($productId)) {
        $productId = parseInt($('input[name="productId"]').val());
      }

      if (isNaN($productId)) {
        let $firstProduct = $('#officeProducts li.nav-item button').first();
        $($firstProduct[0]).trigger('click');
      } else {
        $('button[data-target="#slider-' + $productId + '"]').trigger('click');
      }
    },
    setLoanFormData: function () {
      let $this = this;
      $(document).on('click', 'button[data-target="#slider-' + this.productid + '"]', function () {
        $('input[name="loan[isJuridical]"]').val($this.productSettings.isJuridical);
        $('input[name="loan[isJuridical]"]').trigger('change');

        $('input[name="loan[product_id]"]').val($this.productid);
        $('input[name="loan[loan_sum]"]').val($this.sliderAmount);
        $('input[name="loan[loan_period]"]').val($this.sliderPeriod);
        $('input[name="loan[discount_percent]"]').val($this.sliderDiscount);

        /// view payment schedule
        let $sliderAmount = $this.sliderAmount;
        if ($this.sliderAmount > 0) {
          $sliderAmount = $this.sliderAmount.replace('.', '');
        }
        $('input[name="isJuridical"]').val($this.productSettings.isJuridical);
        $('input[name="isJuridical"]').trigger('change');

        $('input[name="productId"]').val($this.productid);
        // $('input[name="sum"]').val($sliderAmount);
        // $('input[name="period"]').val($this.sliderPeriod);
        // $('input[name="discount"]').val($this.sliderDiscount);
      });
    }
  }
}
</script>
