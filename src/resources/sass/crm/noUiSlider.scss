.noUi-marker-normal {
  display: none;
}

.noUi-marker-large, .noUi-value-large {
  display: none;
}

.noUi-value-large:nth-of-type(2) {
  display: block;
}

.noUi-value-large:last-child {
  left: 96% !important;
  display: block;
}

.noUi-value-horizontal {
  transform: translate(0%, 0%);
}

.noUi-pips {
  padding-top: 5px;
  position: relative;
  top: 0px;
}

.loan-slider {
  .noUi-pips-horizontal {
    height: auto !important;
  }

  strong {
    color: #000000;
  }

  .noUi-target {
    height: 10px !important;
    cursor: pointer;
  }

  .noUi-connect {
    background-color: #0070C0;
  }

  .noUi-handle::before, ::after {
    content: none;
  }

  .noUi-handle {
    border: 0px;
    background-color: #0070C0;
    height: 30px;
    width: 30px;
    top: -12px !important;
    border-radius: 50% !important;
    cursor: grab !important;
    box-shadow: none !important;
  }
}
