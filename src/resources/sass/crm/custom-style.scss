body {
  font-weight: 200 !important;
}

#main-wrapper[data-layout=vertical][data-header-position=fixed] .page-wrapper {
  padding-top: 40px !important;
}

table.table-layout-fixed {
  table-layout: fixed !important;
}

td.installment-date {
  background-color: rgba(112, 173, 71, 0.5) !important;
}

.overflow-x-scroll {
  overflow-x: scroll;
}

.overflow-x-scroll::-webkit-scrollbar {
  display: none;
}

tr.text-danger td {
  color: #E52B06 !important;
}

.page-breadcrumb {
  padding-top: 20px !important;
}

.bg-none {
  background: none;
}

.show-on-hover {
  display: none;
}

.cursor-move {
  display: none;
}

.cursor-pointer {
  cursor: pointer !important;
}

.cursor-help {
  cursor: help !important;
}

.card-body:hover {
  .cursor-move {
    display: block;
  }
}

.sortable {
  .card-title {
    cursor: move !important;
  }
}

.card-header {
  padding: 0.45rem 10px !important;
}

tr.bg-warning {
  background-color: rgba(#fdc16a, 0.5) !important;
}

tr.bg-info {
  background-color: rgba(#0070C0, 0.5) !important;
}

tr.bg-danger-new {
  background-color: rgba(#FCE4D6, 0.5) !important;
}

tr.bg-warning-new {
  background-color: rgba(#f7d8aa80, 0.5) !important;
}

tr.bg-success-new {
  background-color: rgba(#E2EFDA, 0.5) !important;
}

tr.bg-primary-new {
  background-color: rgba(#b6dbe780, 0.5) !important;
}

tr.bg-info-new {
  background-color: rgba(#67a2cb80, 0.5) !important;
}

tr.bg-cyan-new {
  background-color: rgba(#7ad5e780, 0.5) !important;
}

tr.bg-secondary-new {
  background-color: rgba(#c7c9cb, 0.5) !important;
}

tr.bg-danger {
  background-color: rgba(#E52B06, 0.5) !important;
}

tr.bg-handled {
  background-color: rgba(#9baa91, 0.5) !important;
}

td.bg-warning {
  background-color: rgba(#f7d8aa80, 0.5) !important;
}

td.bg-info {
  background-color: rgba(#7cb2d780, 0.5) !important;
}

td.bg-danger {
  background-color: rgba(#E52B06, 0.5) !important;
}

td.bg-danger-new {
  background-color: rgba(#FCE4D6, 0.5) !important;
}

td.bg-warning-new {
  background-color: rgba(#f7ce8f80, 0.5) !important;
}

td.bg-success-new {
  background-color: rgba(#E2EFDA, 0.5) !important;
}

td.bg-primary-new {
  background-color: rgba(#b6dbe780, 0.5) !important;
}

td.bg-info-new {
  background-color: rgba(#7cb2d780, 0.5) !important;
}

td.bg-cyan-new {
  background-color: rgba(#7ad5e780, 0.5) !important;
}

td.bg-secondary-new {
  background-color: rgba(#c7c9cb, 0.5) !important;
}

td.bg-success {
  background-color: rgba(#70AD47, 0.5) !important;
}

td.bg-handled {
  background-color: rgba(#9baa91, 0.5) !important;
}

td.bg-cyan {
  background-color: rgba(#01caf1, 0.5) !important;
}

.no-radius {
  border-radius: 0px !important;
}

#client-card-tab-content {
  /// todo will be remove after include real theme styles
  .tab-pane {
    padding-top: 10px;
    min-height: 100px;
  }
}

.cursor-move {
  cursor: move !important;
}

.control-label {
  color: #000000;
  margin-bottom: 0px !important;
}

.loan-header {
  .rounded {
    border-radius: 15px !important;
  }
}

.loan-header table * {
  color: #000000;
}

.text-dark table tr, td, th {
  color: #000000;
}

td {
  div.form-group {
    margin-bottom: 0px;

    label {
      cursor: pointer;
    }
  }
}


td.button-div {
  white-space: nowrap;
}
