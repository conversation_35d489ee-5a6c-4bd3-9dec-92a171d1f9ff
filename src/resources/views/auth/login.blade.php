@extends('layouts.login')

@section('content')
    <div class="wrapper fadeInDown">
        <div id="formContent">
            <!-- Icon -->
            <div class="fadeIn first">
                @php
                    $project = config('app.project');
                @endphp

                @if ($project === 'stikcredit')
                    <img class="img-fluid" id="logo" src="{{ asset('images/icons/logo.png') }}" alt="homepage"/>
                @else
                    <img class="img-fluid" id="logo" style="padding: 10px;" src="{{ asset('images/icons/logo_lendivo.svg') }}" alt="homepage"/>
                @endif
            </div>

            <!-- Login Form -->
            {!! Form::open(['route' => 'login']) !!}
            <div class="login-form-head">
                <p>Try to login into the best CRM system worldwide!</p>
            </div>
            @if (session('fail'))
                <div>
                    <div class="p-3 mb-2 bg-danger text-white">{{session('fail')}}</div>
                </div>
            @endif

            <div class="form-group">
                <input type="text" id="login" class="fadeIn second" name="username" placeholder="username"/>
            </div>
            <div class="form-group">
                <input type="password" id="password" class="fadeIn third" name="password" placeholder="password">
            </div>
            <input id="form_submit" type="submit" value="{{ __('Login') }}">
            {!! Form::close() !!}

        </div>
        <!-- End ./formContent -->
    </div>
@endsection
