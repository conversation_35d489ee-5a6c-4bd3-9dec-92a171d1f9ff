APP_NAME=credit_hunter_test
APP_ENV=testing
APP_KEY=base64:MZyVsV8zgolDoi9xllLTXCH81si1hrU6iNial3RTZbo=
APP_DEBUG=true
APP_URL=http://sc.crm:8000
APP_USER=user
APP_USER_ID=1001
APP_TZ=Europe/Sofia

PROJECT=stikcredit

WWWUSER=1001
WWWGROUP=1001

LOG_CHANNEL=stack

DB_CONNECTION=pgsql
DB_HOST=postgres
DB_PORT=5432
DB_EXTERNAL_PORT=5432
DB_DATABASE=credit_hunter_db_test
DB_USERNAME=root
DB_PASSWORD=w1ld)H@nt8
POSTGRES_DEFAULT_USER=postgres
POSTGRES_DEFAULT_PASS=postgres!DB_2020
POSTGRES_TEST_DB=credit_hunter_db_test

BROADCAST_DRIVER=redis
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_STORE=redis
SESSION_CONNECTION=session

REDIS_HOST=redis
REDIS_CLIENT=phpredis
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_EXTERNAL_PORT=6379
REDIS_DB=0
REDIS_CACHE_DB=1
REDIS_SESSION_DB=2
REDIS_API_DB=3
REDIS_DOCS_DB=4
REDIS_SMS_DB=5
REDIS_VIBER_DB=6
REDIS_EMAIL_DB=7
REDIS_REPORTS_DB=8
REDIS_WRITE_TIMEOUT=40

ELASTIC_BOOT_PASSWORD=elastic

ELASTICSEARCH_HOST=es
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_SCHEME=http
ELASTICSEARCH_USER=elastic
ELASTICSEARCH_PASS=elastic

MAIL_SENDER="<EMAIL>"
MAIL_SENDER_NAME="Lendivo|STIK"
MAIL_TEST_EMAIL="yourmail"

MAIL_DRIVER=log
MAIL_HOST=smtp.eu.mailgun.org
MAIL_PORT=465

MAIL_USERNAME=
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_PASSWORD=

MAIL_ENCRYPTION=
MAILGUN_DOMAIN=
MAILGUN_SECRET=

API_ALLOWED_IP="Your IP"
API_HASH=U3Rpa0NyZWRpdGlzdGhlYmVzdHBsYWNldG93b3JrYW5kdGhlYm9zc2lzY29vbA==

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=
AWS_BUCKET=

NGINX_PORT=8000
NGINX_PORT_SSL=4430

SMS_SERVICE_TEST_PHONE=0999737622
SMS_SERVICE_MOBICA_URL="https://mobica.bg"
SMS_SERVICE_MOBICA_USER=test
SMS_SERVICE_MOBICA_PASS=password

EASYPAY_PIN="12345"

WEBSITE=http://localhost:8080/
WEBSITE_TOKEN="a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"

TELESCOPE_ENABLED=false

VOLUME_PATH_PG=./data/staging/pg
VOLUME_PATH_REDIS=./data/staging/redis

SELF=.env.testing

########### EASY PAY MICROSERVICE ###########
EASYPAY_MS_TOKEN=
EASYPAY_MS_URL=
# to get local IP: hostname -I | cut -d' ' -f1

#######################################################
### DIRECT SERVICE
DIRECT_SERVICE_HOST=
DIRECT_SERVICE_PORT=
DIRECT_SERVICE_USER=
DIRECT_SERVICE_PASW=

###
MAKE_ITEGROMAT_NEW_CLIENTS_URL=https://hook.eu2.make.com/hvxmj9dfk54565x38pubweko3moav17b

SMART_ITK_LEAD_CREATE_URL=http://partner-staging.smartikt.com/api/lead/create
SMART_ITK_API_KEY=cfad075548934a88fee41dd180c6c8bc231c96bc
